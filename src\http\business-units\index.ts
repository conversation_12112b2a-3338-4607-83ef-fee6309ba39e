import { api } from "@/services/api";
import {
  ICreateBusinessUnit,
  IGetBusinessUnits,
  IGetBusinessUnitById,
  IUpdateBusinessUnit,
} from "./types";

export function createBusinessUnit({
  businessGroupId,
  cnpj,
  alias,
  name,
  address,
  cnae,
  dueDay,
  plans,
}: ICreateBusinessUnit) {
  const response = api.post("/api/business-units", {
    businessGroupId,
    cnpj,
    alias,
    name,
    address,
    cnae,
    dueDay,
    plans,
  });

  return response;
}

export function getBusinessUnits({
  page,
  limit,
  search,
  businessGroupIds,
  dueDayStart,
  dueDayEnd,
  employeeCountStart,
  employeeCountEnd,
}: IGetBusinessUnits) {
  const params = new URLSearchParams();

  if (page !== undefined) params.append("page", page.toString());
  if (limit !== undefined) params.append("limit", limit.toString());
  if (search) params.append("search", search);
  if (businessGroupIds?.length)
    params.append("businessGroupIds", businessGroupIds.join(","));
  if (dueDayStart !== undefined)
    params.append("dueDayStart", String(dueDayStart));
  if (dueDayEnd !== undefined) params.append("dueDayEnd", String(dueDayEnd));
  if (employeeCountStart !== undefined)
    params.append("employeeCountStart", String(employeeCountStart));
  if (employeeCountEnd !== undefined)
    params.append("employeeCountEnd", String(employeeCountEnd));

  const response = api.get(`/api/business-units?${params.toString()}`);

  return response;
}

export function getBusinessUnitById({ id }: IGetBusinessUnitById) {
  const response = api.get(`/api/business-units/${id}`);

  return response;
}

export function updateBusinessUnit({
  id,
  alias,
  name,
  address,
  cnae,
  dueDay,
  plans,
}: IUpdateBusinessUnit) {
  const response = api.put(`/api/business-units/${id}`, {
    alias,
    name,
    address,
    cnae,
    dueDay,
    plans,
  });

  return response;
}
