export interface Union {
  id: string;
  cnpj: string;
  cpfCnpj: string;
  alias: string;
  name: string;
  registrationStatus: string;
  address: {
    street: string;
    number: string;
    complement?: string;
    zip: string;
    neighborhood: string;
    city: string;
    state: string;
  };
  contact: {
    name: string;
    phone: string;
    email: string;
  };
  financeContact?: {
    name: string;
    phone: string;
    email: string;
  };
  bank: {
    id: string;
    name: string;
    code: string;
  };
  agency: string;
  accountNumber: string;
  pixKey: string;
  commissionPercentage: number;
  commissionPaymentDay: number;
  plans: Array<{
    id: string;
    name: string;
    value: number;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUnionRequest {
  cnpj: string;
  cpfCnpj: string;
  alias: string;
  name: string;
  registrationStatus: string;
  address: {
    street: string;
    number: string;
    complement?: string;
    zip: string;
    neighborhood: string;
    city: string;
    state: string;
  };
  contact: {
    name: string;
    phone: string;
    email: string;
  };
  financeContact?: {
    name: string;
    phone: string;
    email: string;
  };
  bankId: string;
  agency: string;
  accountNumber: string;
  pixKey?: string;
  commissionPercentage: number;
  commissionPaymentDay: number;
  plans?: Array<{
    id: string;
    value: number;
  }>;
}

export interface EditUnionRequest {
  alias?: string;
  contact?: {
    name: string;
    phone: string;
    email: string;
  };
  financeContact?: {
    name?: string;
    phone?: string;
    email?: string;
  };
  bankId?: string;
  agency?: string;
  accountNumber?: string;
  pixKey?: string;
  commissionPercentage?: number;
  commissionPaymentDay?: number;
  plans?: Array<{
    id: string;
    value: number;
  }>;
}

export interface GetUnionsParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface GetUnionsResponse {
  unions: Union[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateUnionResponse {
  id: string;
}
