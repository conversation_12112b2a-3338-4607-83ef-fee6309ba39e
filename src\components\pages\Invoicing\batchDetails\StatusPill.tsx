type StatusPillProps = {
  label: string;
  variant: "success" | "error" | "warning" | "neutral";
  className?: string;
};

export function StatusPill({ label, variant, className }: StatusPillProps) {
  const base =
    "inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-medium";
  const map: Record<StatusPillProps["variant"], string> = {
    success:
      "bg-emerald-100 text-emerald-700 dark:bg-emerald-500/15 dark:text-emerald-400",
    error: "bg-red-100 text-red-700 dark:bg-red-500/15 dark:text-red-400",
    warning:
      "bg-amber-100 text-amber-700 dark:bg-amber-500/15 dark:text-amber-400",
    neutral: "bg-gray-100 text-gray-600 dark:bg-gray-500/15 dark:text-gray-300",
  };

  return (
    <span className={`${base} ${map[variant]} ${className || ""}`}>
      {label}
    </span>
  );
}
