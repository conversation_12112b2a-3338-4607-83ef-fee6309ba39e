import axios, { AxiosError, InternalAxiosRequestConfig } from "axios";

// Storage utility (same as auth context)
const getAccessToken = (): string | null => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("@gestao-sindicatos:access-token");
  }
  return null;
};

const getRefreshToken = (): string | null => {
  if (typeof window !== "undefined") {
    return localStorage.getItem("@gestao-sindicatos:refresh-token");
  }
  return null;
};

const setTokens = (accessToken: string, refreshToken: string) => {
  if (typeof window !== "undefined") {
    localStorage.setItem("@gestao-sindicatos:access-token", accessToken);
    localStorage.setItem("@gestao-sindicatos:refresh-token", refreshToken);
  }
};

const clearTokens = () => {
  if (typeof window !== "undefined") {
    localStorage.removeItem("@gestao-sindicatos:access-token");
    localStorage.removeItem("@gestao-sindicatos:refresh-token");
    localStorage.removeItem("@gestao-sindicatos:user");
  }
};

// Base URL shared across clients
const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

// Public axios instance (no interceptors)
export const publicApi = axios.create({
  baseURL: BASE_URL,
});

// Private axios instance (with interceptors and auth)
export const api = axios.create({
  baseURL: BASE_URL,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getAccessToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean;
    };

    // If error is 401 and we haven't already tried to refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = getRefreshToken();

      if (refreshToken) {
        try {
          const refreshResponse = await axios.post(
            `${api.defaults.baseURL}/api/auth/refresh-token`,
            {
              refreshToken,
            }
          );

          const { accessToken, refreshToken: newRefreshToken } =
            refreshResponse.data;

          // Update stored tokens
          setTokens(accessToken, newRefreshToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect to login
          clearTokens();

          if (typeof window !== "undefined") {
            window.location.href = "/";
          }

          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token, clear everything and redirect
        clearTokens();

        if (typeof window !== "undefined") {
          window.location.href = "/";
        }
      }
    }

    return Promise.reject(error);
  }
);
