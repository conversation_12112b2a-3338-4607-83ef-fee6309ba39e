"use client";

import React from "react";
import { useState, useEffect } from "react";
import { InvoicingBatch } from "./types";
import { BatchCard } from "./BatchCard";
import { InlineSpinner } from "@/components/ui/inline-spinner";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
  PaginationStart,
  PaginationEnd,
} from "@/components/ui/pagination";

export function BatchesList() {
  const [isLoading, setIsLoading] = useState(true);
  const [batches, setBatches] = useState<InvoicingBatch[]>([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  const fetchBatches = async () => {
    try {
      setIsLoading(true);
      const { getBillingBatches } = await import("@/http/billing");
      const res = await getBillingBatches({ page, limit: pageSize });

      const mapped: InvoicingBatch[] = res.data.map((b) => ({
        id: b.id,
        sequenceNumber: b.sequenceNumber,
        status: (b.status as InvoicingBatch["status"]) ?? "PROCESSING",
        progress: Math.min(100, Math.max(0, Number(b.progress ?? 0))),
        totals: {
          total: b.totalInvoices ?? 0,
          error: b.totalFailedInvoices ?? 0,
          success: b.totalSuccessfulInvoices ?? 0,
        },
        generatedAt: b.generatedAt,
      }));

      setBatches(mapped);
      setTotalPages(res.pagination.totalPages || 1);
      // eslint-disable-next-line
    } catch (err) {
      setBatches([]);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBatches();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize]);

  useEffect(() => {
    const handler = () => {
      setPage(1);
      fetchBatches();
    };
    window.addEventListener("refresh-batches", handler);
    return () => window.removeEventListener("refresh-batches", handler);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fallback: enquanto houver lotes em processamento, atualizar a lista a cada 10s
  useEffect(() => {
    const hasProcessing = batches.some(
      (b) => b.status === "PENDING" || b.status === "PROCESSING"
    );
    if (!hasProcessing) return;
    const id = setInterval(() => {
      fetchBatches();
    }, 10000);
    return () => clearInterval(id);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(batches.map((b) => ({ id: b.id, status: b.status })))]);

  return (
    <div className="flex flex-col min-h-[calc(100vh-8rem)] w-full p-6 gap-4">
      {isLoading ? (
        <div className="w-full flex items-center justify-center p-12">
          <InlineSpinner size="lg" ariaLabel="Carregando lotes" />
        </div>
      ) : batches.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-semibold text-gray-700">
              Ainda não existe nenhuma fatura gerada
            </div>
            <div className="text-gray-500 mt-2">
              Clique no botão “Novo faturamento” para criar o primeiro registro
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {batches.map((b) => (
            <BatchCard key={b.id} batch={b} />
          ))}
        </div>
      )}

      {batches.length > 0 && (
        <div className="mt-auto flex w-full items-center justify-center py-4">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700">Exibir</span>
              <div className="w-fit">
                <select
                  className="h-9 px-3 w-auto border rounded-sm shadow-sm text-sm"
                  value={pageSize}
                  onChange={(e) => {
                    setPageSize(Number(e.target.value));
                    setPage(1);
                  }}
                >
                  {[10, 25, 50, 100].map((n) => (
                    <option key={n} value={n}>
                      {n} por página
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationStart
                    href="#"
                    onClick={() => setPage(1)}
                    aria-disabled={page === 1}
                    className={
                      page === 1 ? "pointer-events-none opacity-50" : undefined
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={() => setPage(Math.max(1, page - 1))}
                    aria-disabled={page === 1}
                    className={
                      page === 1 ? "pointer-events-none opacity-50" : undefined
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <span className="text-sm text-teal-600 px-2 font-semibold">
                    {page}
                  </span>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={() => setPage(Math.min(totalPages, page + 1))}
                    aria-disabled={page === totalPages}
                    className={
                      page === totalPages
                        ? "pointer-events-none opacity-50"
                        : undefined
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <PaginationEnd
                    href="#"
                    onClick={() => setPage(totalPages)}
                    aria-disabled={page === totalPages}
                    className={
                      page === totalPages
                        ? "pointer-events-none opacity-50"
                        : undefined
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
}
