"use client";

import { Modal } from "@/components/modals";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, FormProvider, useForm } from "react-hook-form";
import {
  contabilityValidation,
  ContabilityValidationType,
} from "./validations";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { getCompanyByCNPJ } from "@/http/external";
import {
  getConsultancies,
  createConsultancy,
  updateConsultancy,
  getConsultancyById,
} from "@/http/consultancies";
import { formatCNPJ, maskCNPJ, unformatCNPJ, maskPhoneBR } from "@/lib/format";
import { useToast } from "@/hooks/use-toast";
import { useDebounce } from "@/hooks/use-debounce";
import { useQueryClient } from "@tanstack/react-query";
import { SuccessModal } from "@/components/modals/success-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { InlineSpinner } from "@/components/ui/inline-spinner";

type ContabilityModalFormProps = {
  triggerButton: React.ReactNode;
  isEditMode?: boolean;
  consultancy?: {
    id: string;
    cnpj: string;
    alias: string;
    name: string;
    address?: {
      street?: string;
      number?: string;
      complement?: string;
      zip?: string;
      neighborhood?: string;
      city?: string;
      state?: string;
    };
    contact?: { name?: string; phone?: string; email?: string };
    isActive?: boolean;
  } | null;
};

export function ContabilityModalForm({
  triggerButton,
  isEditMode,
  consultancy,
}: ContabilityModalFormProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isFetchingCompany, setIsFetchingCompany] = useState<boolean>(false);
  const queryClient = useQueryClient();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successTitle, setSuccessTitle] = useState("");
  const [isHydratingEdit, setIsHydratingEdit] = useState<boolean>(false);
  void isHydratingEdit;

  const form = useForm<ContabilityValidationType>({
    resolver: zodResolver(contabilityValidation),
    defaultValues:
      isEditMode && consultancy
        ? {
            cnpj: consultancy.cnpj,
            alias: consultancy.alias,
            name: consultancy.name,
            address: {
              street: consultancy.address?.street || "",
              number: consultancy.address?.number || "",
              complement: consultancy.address?.complement || "",
              zip: consultancy.address?.zip || "",
              neighborhood: consultancy.address?.neighborhood || "",
              city: consultancy.address?.city || "",
              state: consultancy.address?.state || "",
            } as {
              street: string;
              number: string;
              complement?: string;
              zip: string;
              neighborhood: string;
              city: string;
              state: string;
            },
            contact: {
              name: consultancy.contact?.name || "",
              phone: consultancy.contact?.phone || "",
              email: consultancy.contact?.email || "",
            },
            isActive: consultancy.isActive ?? true,
          }
        : {
            isActive: true,
            contact: {
              name: "",
              phone: "",
              email: "",
            },
          },
  });

  const hasCompanyData = Boolean(
    form.watch("name") && form.watch("address.street")
  );

  const debouncedCnpj = useDebounce(form.watch("cnpj") || "", 400);

  useEffect(() => {
    let cancelled = false;
    const fetchData = async () => {
      if (isEditMode) return;
      if (!debouncedCnpj || debouncedCnpj.length !== 14) return;
      const isValidCnpjOnly = await form.trigger(["cnpj"]);
      if (!isValidCnpjOnly) return;
      try {
        setIsFetchingCompany(true);
        // 1) Verifica se já existe contabilidade com este CNPJ
        try {
          const { data } = await getConsultancies({ search: debouncedCnpj });
          const consultancies = data?.consultancies || [];
          const exists = consultancies.some(
            (c) => String(c.cnpj || "").replace(/\D/g, "") === debouncedCnpj
          );
          if (exists) {
            form.setError("cnpj", {
              type: "manual",
              message: "Já existe uma contabilidade cadastrada com este CNPJ",
            });
            return; // não busca dados externos
          } else {
            form.clearErrors("cnpj");
          }
        } catch {
          // Em caso de erro na verificação, não bloqueia a busca externa
        }

        // 2) Busca dados externos
        const company = await getCompanyByCNPJ(debouncedCnpj);
        if (cancelled) return;
        form.setValue("alias", company.alias);
        form.setValue("name", company.name);
        form.setValue(
          "address",
          company.address as {
            street: string;
            number: string;
            complement?: string;
            zip: string;
            neighborhood: string;
            city: string;
            state: string;
          }
        );
      } catch (error: unknown) {
        const { extractApiErrorMessage } = await import("@/lib/utils");
        toast({
          title: "Erro",
          description: extractApiErrorMessage(
            error,
            "Erro ao buscar dados da empresa"
          ),
          variant: "destructive",
        });
      } finally {
        setIsFetchingCompany(false);
      }
    };
    fetchData();
    return () => {
      cancelled = true;
    };
  }, [debouncedCnpj, form, toast, isEditMode]);

  useEffect(() => {
    let cancelled = false;
    (async () => {
      if (!isOpen || !isEditMode || !consultancy?.id) return;
      try {
        setIsHydratingEdit(true);
        const res = await getConsultancyById({ id: consultancy.id });
        const full = (res as unknown as { data?: unknown }).data ?? res;
        const entity = full as unknown as {
          cnpj?: string;
          alias?: string;
          name?: string;
          address?: {
            street?: string;
            number?: string;
            complement?: string;
            zip?: string;
            neighborhood?: string;
            city?: string;
            state?: string;
          };
          contact?: { name?: string; phone?: string; email?: string };
          isActive?: boolean;
        };
        if (cancelled) return;
        form.reset({
          cnpj: entity.cnpj || consultancy.cnpj,
          alias: entity.alias || consultancy.alias,
          name: entity.name || consultancy.name,
          address: {
            street: entity.address?.street || "",
            number: entity.address?.number || "",
            complement: entity.address?.complement || "",
            zip: entity.address?.zip || "",
            neighborhood: entity.address?.neighborhood || "",
            city: entity.address?.city || "",
            state: entity.address?.state || "",
          },
          contact: {
            name: entity.contact?.name || consultancy.contact?.name || "",
            phone: entity.contact?.phone || consultancy.contact?.phone || "",
            email: entity.contact?.email || consultancy.contact?.email || "",
          },
          isActive: entity.isActive ?? consultancy.isActive ?? true,
        });
      } catch {
        // silêncio: se falhar hidratação, mantém valores existentes
      } finally {
        setIsHydratingEdit(false);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [isOpen, isEditMode, consultancy?.id, consultancy, form]);

  const handleSubmitContability = async (data: ContabilityValidationType) => {
    const fieldsToValidate: (keyof ContabilityValidationType)[] = [
      "cnpj",
      "alias",
      "name",
      "address",
      "contact",
      "isActive",
    ];
    const valid = await form.trigger(fieldsToValidate);
    if (!valid) return;

    try {
      if (isEditMode && consultancy?.id) {
        await updateConsultancy({
          id: consultancy.id,
          alias: data.alias,
          name: data.name,
          address: {
            street: data.address.street,
            number: data.address.number,
            complement: data.address.complement,
            zip: data.address.zip,
            neighborhood: data.address.neighborhood,
            city: data.address.city,
            state: data.address.state,
          },
          contact: {
            name: data.contact.name,
            phone: data.contact.phone,
            email: data.contact.email,
          },
          isActive: !!data.isActive,
        });
      } else {
        await createConsultancy({
          cnpj: unformatCNPJ(data.cnpj),
          alias: data.alias,
          name: data.name,
          address: {
            street: data.address.street,
            number: data.address.number,
            complement: data.address.complement,
            zip: data.address.zip,
            neighborhood: data.address.neighborhood,
            city: data.address.city,
            state: data.address.state,
          },
          contact: {
            name: data.contact.name,
            phone: data.contact.phone,
            email: data.contact.email,
          },
          isActive: !!data.isActive,
        });
      }

      queryClient.invalidateQueries({ queryKey: ["consultancies"] });

      setSuccessTitle(
        isEditMode
          ? "Contabilidade atualizada com sucesso!"
          : "Contabilidade cadastrada com sucesso!"
      );
      setShowSuccessModal(true);
      setIsOpen(false);
      form.reset({
        isActive: true,
        contact: { name: "", phone: "", email: "" },
      });
    } catch (error: unknown) {
      const err = error as { response?: { data?: { message?: string } } };
      toast({
        title: "Erro",
        description:
          err?.response?.data?.message || "Erro ao cadastrar contabilidade",
        variant: "destructive",
      });
    }
  };

  const cnpjMasked = maskCNPJ(form.watch("cnpj") || "");

  return (
    <>
      <Modal
        title={isEditMode ? "Editar contabilidade" : "Nova contabilidade"}
        triggerButton={triggerButton}
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open)
            form.reset(
              isEditMode && consultancy
                ? {
                    cnpj: consultancy.cnpj,
                    alias: consultancy.alias,
                    name: consultancy.name,
                    address: {
                      street: consultancy.address?.street || "",
                      number: consultancy.address?.number || "",
                      complement: consultancy.address?.complement || "",
                      zip: consultancy.address?.zip || "",
                      neighborhood: consultancy.address?.neighborhood || "",
                      city: consultancy.address?.city || "",
                      state: consultancy.address?.state || "",
                    } as {
                      street: string;
                      number: string;
                      complement?: string;
                      zip: string;
                      neighborhood: string;
                      city: string;
                      state: string;
                    },
                    contact: {
                      name: consultancy.contact?.name || "",
                      phone: consultancy.contact?.phone || "",
                      email: consultancy.contact?.email || "",
                    },
                    isActive: consultancy.isActive ?? true,
                  }
                : {
                    isActive: true,
                    contact: { name: "", phone: "", email: "" },
                  }
            );
        }}
      >
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-6"
            onSubmit={form.handleSubmit(handleSubmitContability)}
          >
            <div className="flex items-center gap-2">
              <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5">
                Identificação
              </span>
            </div>

            <div className="flex flex-col gap-6">
              <Input
                label="CNPJ"
                placeholder="Digite o CNPJ"
                trailingIcon={isFetchingCompany ? <InlineSpinner /> : null}
                value={
                  isEditMode ? formatCNPJ(form.watch("cnpj") || "") : cnpjMasked
                }
                onChange={
                  isEditMode
                    ? undefined
                    : (e) => {
                        const v = e.target.value;
                        form.setValue("cnpj", unformatCNPJ(v));
                      }
                }
                errorMessage={form.formState.errors.cnpj?.message as string}
                disabled={!!isEditMode || isFetchingCompany}
                readOnly={!!isEditMode}
              />

              {isFetchingCompany ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Input
                  label="Nome fantasia"
                  placeholder=""
                  value={form.watch("alias") || "Preenchido automaticamente"}
                  readOnly
                  disabled
                />
              )}
              {isFetchingCompany ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Input
                  label="Razão Social"
                  placeholder=""
                  value={form.watch("name") || "Preenchido automaticamente"}
                  readOnly
                  disabled
                />
              )}
              {isFetchingCompany ? (
                <Skeleton className="h-24 w-full" />
              ) : (
                <Textarea
                  label="Endereço"
                  value={
                    form.watch("address")
                      ? `${form.watch("address.street")}, ${form.watch(
                          "address.number"
                        )}\n${form.watch("address.complement") || ""}\n${
                          form.watch("address.neighborhood") || ""
                        }\n${form.watch("address.city")}, ${form.watch(
                          "address.state"
                        )}\n${form.watch("address.zip")}`
                      : "Preenchido automaticamente"
                  }
                  readOnly
                  disabled
                />
              )}
            </div>

            <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-2">
              Contato Principal
            </span>

            <div className="flex flex-col gap-6">
              <Input
                label="Nome"
                placeholder="Digite o nome"
                {...form.register("contact.name")}
                errorMessage={
                  form.formState.errors?.contact?.name?.message as string
                }
              />
              {(() => {
                const contactPhoneReg = form.register("contact.phone");
                return (
                  <Input
                    label="Telefone"
                    placeholder="Digite o telefone"
                    name={contactPhoneReg.name}
                    ref={contactPhoneReg.ref}
                    onBlur={contactPhoneReg.onBlur}
                    onChange={(e) => {
                      e.target.value = maskPhoneBR(e.target.value);
                      contactPhoneReg.onChange(e);
                    }}
                    onInput={(e) => {
                      const el = e.target as HTMLInputElement;
                      el.value = maskPhoneBR(el.value);
                      contactPhoneReg.onChange(
                        e as unknown as React.ChangeEvent<HTMLInputElement>
                      );
                    }}
                    errorMessage={
                      form.formState.errors?.contact?.phone?.message as string
                    }
                  />
                );
              })()}
              <Input
                label="Email"
                placeholder="Digite o email"
                {...form.register("contact.email")}
                errorMessage={
                  form.formState.errors?.contact?.email?.message as string
                }
              />
            </div>

            <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-2">
              Status
            </span>
            <div className="flex flex-col gap-6 mb-4">
              <Controller
                name="isActive"
                control={form.control}
                render={({ field }) => (
                  <RadioGroup
                    value={field.value ? "active" : "inactive"}
                    onValueChange={(v) => field.onChange(v === "active")}
                  >
                    <div className="flex gap-4">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="active" id="active" />
                        <Label htmlFor="active" className="cursor-pointer">
                          Ativo
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="inactive" id="inactive" />
                        <Label htmlFor="inactive" className="cursor-pointer">
                          Inativo
                        </Label>
                      </div>
                    </div>
                  </RadioGroup>
                )}
              />
            </div>

            <Button
              type="submit"
              variant="secondary"
              isLoading={isFetchingCompany || form.formState.isSubmitting}
              disabled={
                isFetchingCompany ||
                form.formState.isSubmitting ||
                !hasCompanyData ||
                !!form.formState.errors.cnpj ||
                (!isEditMode &&
                  (form.watch("cnpj") || "").replace(/\D/g, "").length < 14)
              }
            >
              {isFetchingCompany
                ? "Buscando..."
                : isEditMode
                ? "Salvar"
                : "Cadastrar"}
            </Button>
          </form>
        </FormProvider>
      </Modal>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={successTitle}
      />
    </>
  );
}
