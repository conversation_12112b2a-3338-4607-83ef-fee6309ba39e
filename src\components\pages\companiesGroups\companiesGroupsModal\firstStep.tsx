/* eslint-disable react-hooks/exhaustive-deps */
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/selectUi";
import { Controller, useFormContext } from "react-hook-form";
import { useEffect, useMemo, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getUnions } from "@/http/unions";
import { getConsultancies } from "@/http/consultancies";
import {
  GetConsultanciesResponse,
  Consultancy,
} from "@/http/consultancies/types";
import { LabelAndValue } from "@/types/labelAndValue";
import { getCompanyByCNPJ } from "@/http/external";
import { useToast } from "@/hooks/use-toast";
import { getBusinessGroups } from "@/http/business-groups";
import { maskCNPJ, unformatCNPJ, maskCEP } from "@/lib/format";
import { useDebounce } from "@/hooks/use-debounce";
import { extractApiErrorMessage } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { InlineSpinner } from "@/components/ui/inline-spinner";

type FirstStepProps = { isEditMode?: boolean };

export function FirstStep({ isEditMode }: FirstStepProps) {
  const {
    register,
    control,
    formState: { errors },
    watch,
    setValue,
    setError,
    clearErrors,
  } = useFormContext();
  const { toast } = useToast();

  const [cnpjDisplay, setCnpjDisplay] = useState("");
  const debouncedCnpjDigits = useDebounce(unformatCNPJ(cnpjDisplay), 400);
  const [isFetchingCompany, setIsFetchingCompany] = useState(false);

  useEffect(() => {
    const current = watch("cnpj") as string | undefined;
    if (current && !cnpjDisplay) {
      setCnpjDisplay(maskCNPJ(current));
    }
  }, []);

  const [unionSearch, setUnionSearch] = useState("");
  const debouncedUnionSearch = useDebounce(unionSearch, 300);
  const [unionPage, setUnionPage] = useState(1);
  const [unionItems, setUnionItems] = useState<LabelAndValue[]>([]);
  const { data: unionsData, isFetching: isFetchingUnions } = useQuery<
    import("@/http/unions/types").GetUnionsResponse
  >({
    queryKey: ["unions", debouncedUnionSearch, unionPage],
    queryFn: () =>
      getUnions({
        page: unionPage,
        limit: 10,
        search: debouncedUnionSearch || undefined,
      }),
  });

  useEffect(() => {
    setUnionItems([]);
    setUnionPage(1);
  }, [debouncedUnionSearch]);

  const [consultancySearch, setConsultancySearch] = useState("");
  const debouncedConsultancySearch = useDebounce(consultancySearch, 300);
  const [consultancyPage, setConsultancyPage] = useState(1);
  const [consultancyItems, setConsultancyItems] = useState<LabelAndValue[]>([]);
  const { data: consultanciesData, isFetching: isFetchingConsultancies } =
    useQuery<GetConsultanciesResponse>({
      queryKey: ["consultancies", debouncedConsultancySearch, consultancyPage],
      queryFn: async () => {
        const res = await getConsultancies({
          page: consultancyPage,
          limit: 10,
          search: debouncedConsultancySearch || undefined,
        });
        const payload =
          (res as unknown as { data?: GetConsultanciesResponse }).data ??
          (res as unknown as GetConsultanciesResponse);
        return payload;
      },
    });

  useEffect(() => {
    setConsultancyItems([]);
    setConsultancyPage(1);
  }, [debouncedConsultancySearch]);

  const unionOptionsPage: LabelAndValue[] = useMemo(
    () =>
      (unionsData?.unions || []).map((u) => ({ label: u.alias, value: u.id })),
    [unionsData?.unions]
  );
  useEffect(() => {
    if (unionOptionsPage.length) {
      setUnionItems((prev) => {
        const existing = new Set(prev.map((o) => o.value));
        return [
          ...prev,
          ...unionOptionsPage.filter((o) => !existing.has(o.value)),
        ];
      });
    }
  }, [unionOptionsPage]);

  const consultancyOptionsPage: LabelAndValue[] = useMemo(
    () =>
      (consultanciesData?.consultancies || []).map((c: Consultancy) => ({
        label: c.alias ?? c.name ?? "",
        value: String(c.id ?? ""),
      })),
    [consultanciesData?.consultancies]
  );
  useEffect(() => {
    if (consultancyOptionsPage.length) {
      setConsultancyItems((prev) => {
        const existing = new Set(prev.map((o) => o.value));
        return [
          ...prev,
          ...consultancyOptionsPage.filter((o) => !existing.has(o.value)),
        ];
      });
    }
  }, [consultancyOptionsPage]);

  useEffect(() => {
    if (isEditMode) return;
    const digits = debouncedCnpjDigits;
    if (!digits || digits.length !== 14) return;
    let isCancelled = false;
    (async () => {
      try {
        setIsFetchingCompany(true);
        const res = await getBusinessGroups({ search: digits });
        const payload =
          (
            res as unknown as {
              data?: { businessGroups?: Array<{ cnpj?: string }> };
            }
          ).data ||
          (res as unknown as { businessGroups?: Array<{ cnpj?: string }> });
        const list = (payload.businessGroups || []) as Array<{
          cnpj?: string;
        }>;
        const exists = list.some(
          (g) => (g.cnpj || "").replace(/\D/g, "") === digits
        );
        if (exists) {
          setError("cnpj", {
            type: "manual",
            message: "Já existe um grupo empresarial com este CNPJ",
          });
          return;
        }
        clearErrors("cnpj");

        const company = await getCompanyByCNPJ(digits);
        if (isCancelled) return;

        const addrParts: string[] = [];
        if (company.address?.street && company.address?.number) {
          addrParts.push(
            `${company.address.street}, ${company.address.number}`
          );
        }
        if (company.address?.complement)
          addrParts.push(company.address.complement);
        if (company.address?.neighborhood)
          addrParts.push(company.address.neighborhood);
        const cityState = [company.address?.city, company.address?.state]
          .filter(Boolean)
          .join(", ");
        if (cityState) addrParts.push(cityState);
        if (company.address?.zip) addrParts.push(maskCEP(company.address.zip));

        setValue("socialReason", company.name);
        setValue("fantasyName", company.alias);
        setValue("address", addrParts.join(" | "));
        setValue("addressObj", {
          street: company.address.street,
          number: company.address.number,
          complement: company.address.complement,
          zip: company.address.zip,
          neighborhood: company.address.neighborhood,
          city: company.address.city,
          state: company.address.state,
        });
        setValue("cnae", company.cnae);
      } catch (error: unknown) {
        toast({
          title: "Erro",
          description: extractApiErrorMessage(
            error,
            "Erro ao buscar dados da empresa"
          ),
          variant: "destructive",
        });
      } finally {
        setIsFetchingCompany(false);
      }
    })();
    return () => {
      isCancelled = true;
    };
  }, [debouncedCnpjDigits, isEditMode, setValue, setError, clearErrors, toast]);

  return (
    <div className="flex flex-col gap-8">
      <Controller
        name="union"
        control={control}
        render={({ field }) => (
          <Select
            label="Sindicato vinculado"
            placeholder="Selecione o sindicato"
            options={unionItems}
            onChange={field.onChange}
            value={field.value}
            errorMessage={errors?.union?.message as string}
            searchable
            searchPlaceholder="Pesquise pelo sindicato"
            searchValue={unionSearch}
            onSearchChange={setUnionSearch}
            isLoading={isFetchingUnions}
            onLoadMore={() => setUnionPage((p) => p + 1)}
            hasMore={(unionsData?.totalPages || 1) > unionPage}
            isLoadingMore={isFetchingUnions}
            disabled={!!field.value}
          />
        )}
      />
      <Controller
        name="consultancy"
        control={control}
        render={({ field }) => (
          <Select
            label="Contabilidade Vinculada"
            placeholder="Selecione a contabilidade"
            options={consultancyItems}
            onChange={field.onChange}
            value={field.value}
            errorMessage={errors?.consultancy?.message as string}
            searchable
            searchPlaceholder="Pesquise pela contabilidade"
            searchValue={consultancySearch}
            onSearchChange={setConsultancySearch}
            isLoading={isFetchingConsultancies}
            onLoadMore={() => setConsultancyPage((p) => p + 1)}
            hasMore={(consultanciesData?.totalPages || 1) > consultancyPage}
            isLoadingMore={isFetchingConsultancies}
            disabled={!!field.value}
          />
        )}
      />
      <Input
        label="CNPJ"
        placeholder={isEditMode ? undefined : "Digite o CNPJ"}
        trailingIcon={isFetchingCompany ? <InlineSpinner /> : null}
        value={isEditMode ? maskCNPJ(watch("cnpj") || "") : cnpjDisplay}
        onChange={
          isEditMode
            ? undefined
            : (e) => {
                const masked = maskCNPJ(e.target.value);
                setCnpjDisplay(masked);
                setValue("cnpj", unformatCNPJ(masked));
              }
        }
        errorMessage={errors.cnpj?.message as string}
        disabled={!!isEditMode || isFetchingCompany}
        readOnly={!!isEditMode}
      />
      {isFetchingCompany ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="Razão Social"
          value={watch("socialReason") || "Preenchido automaticamente"}
          readOnly
          disabled
          {...register("socialReason")}
          errorMessage={errors.socialReason?.message as string}
        />
      )}
      {isFetchingCompany ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="Nome fantasia"
          value={watch("fantasyName") || "Preenchido automaticamente"}
          readOnly
          disabled
          {...register("fantasyName")}
          errorMessage={errors.fantasyName?.message as string}
        />
      )}
      {isFetchingCompany ? (
        <Skeleton className="h-24 w-full" />
      ) : (
        (() => {
          const addressObj = watch("addressObj") as
            | {
                street?: string;
                number?: string;
                complement?: string;
                zip?: string;
                neighborhood?: string;
                city?: string;
                state?: string;
              }
            | undefined;

          const parts: string[] = [];
          if (addressObj?.street && addressObj?.number)
            parts.push(`${addressObj.street}, ${addressObj.number}`);
          if (addressObj?.complement) parts.push(addressObj.complement);
          if (addressObj?.neighborhood) parts.push(addressObj.neighborhood);
          const cityState = [addressObj?.city, addressObj?.state]
            .filter(Boolean)
            .join(", ");
          if (cityState) parts.push(cityState);
          if (addressObj?.zip) parts.push(maskCEP(addressObj.zip));

          const text = parts.length
            ? parts.join("\n")
            : watch("address") || "Preenchido automaticamente";

          return (
            <Textarea
              label="Endereço"
              value={text}
              readOnly
              disabled
              className="whitespace-pre-wrap"
            />
          );
        })()
      )}
      {isFetchingCompany ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="CNAE"
          value={watch("cnae") || "Preenchido automaticamente"}
          readOnly
          disabled
          {...register("cnae")}
          errorMessage={errors.cnae?.message as string}
        />
      )}
    </div>
  );
}
