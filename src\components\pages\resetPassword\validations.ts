import { errorMessages } from "@/helpers/errorMessages";
import { z } from "zod";

const SPECIAL_CHAR_REGEX = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>\/?]/;

export const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(1, errorMessages.required)
      .min(10, "A senha deve ter no mínimo 10 caracteres")
      .regex(/[A-Z]/, "A senha deve conter pelo menos uma letra maiúscula")
      .regex(
        SPECIAL_CHAR_REGEX,
        "A senha deve conter pelo menos um caractere especial"
      ),
    confirmPassword: z.string().min(1, errorMessages.required),
  })
  .superRefine((data, ctx) => {
    if (data.password !== data.confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "As senhas devem coincidir",
        path: ["confirmPassword"],
      });
    }
  });

export type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;
