import { REQUIRED_MESSAGE } from "@/validations";
import { z } from "zod";

const addressSchema = z.object({
  street: z.string().min(1, { message: REQUIRED_MESSAGE }),
  number: z.string().min(1, { message: REQUIRED_MESSAGE }),
  complement: z.string().optional(),
  zip: z.string().min(1, { message: REQUIRED_MESSAGE }),
  neighborhood: z.string().min(1, { message: REQUIRED_MESSAGE }),
  city: z.string().min(1, { message: REQUIRED_MESSAGE }),
  state: z.string().min(1, { message: REQUIRED_MESSAGE }),
});

const planCountSchema = z.object({
  planId: z.string().min(1, { message: REQUIRED_MESSAGE }),
  employeeCount: z
    .number({ required_error: REQUIRED_MESSAGE })
    .int({ message: "Informe um número inteiro" })
    .min(0, { message: "Não pode ser negativo" }),
});

export const businessUnitValidation = z.object({
  businessGroup: z
    .any({ required_error: REQUIRED_MESSAGE })
    .refine((v) => Boolean(v), { message: REQUIRED_MESSAGE }),
  cnpj: z.string().min(1, { message: REQUIRED_MESSAGE }),
  fantasyName: z.string().min(1, { message: REQUIRED_MESSAGE }),
  socialReason: z.string().min(1, { message: REQUIRED_MESSAGE }).optional(),
  addressObj: addressSchema.optional(),
  cnae: z.string().min(1, { message: REQUIRED_MESSAGE }).optional(),
  dueDay: z
    .number({ required_error: REQUIRED_MESSAGE })
    .min(1, { message: "Dia deve estar entre 1 e 31" })
    .max(31, { message: "Dia deve estar entre 1 e 31" }),
  plans: z
    .array(planCountSchema)
    .min(1, { message: "Informe as quantidades para os planos" })
    .refine((arr) => arr.every((p) => typeof p.employeeCount === "number"), {
      message: "Todos os planos devem ser preenchidos",
    }),
});

export type BusinessUnitValidationType = z.infer<typeof businessUnitValidation>;
