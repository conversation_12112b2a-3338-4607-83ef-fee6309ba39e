"use client";

import { useState } from "react";
import { DataTable } from "../ui/datatable";
import { Button } from "../ui/button";
import { Edit2 } from "lucide-react";
import { Column } from "@/types/table";
import { useQuery } from "@tanstack/react-query";
import { getBusinessGroups } from "@/http/business-groups";
import { formatCNPJ, maskPhoneBR } from "@/lib/format";
import { CompaniesGroupModalForm } from "../pages/companiesGroups/companiesGroupsModal";

type CompaniesGroupsTableProps = { search?: string };

type BusinessGroupRow = {
  id: string;
  cnpj: string;
  alias: string;
  name: string;
  contact: { name: string; email: string; phone: string };
};

export function CompaniesGroupsTable({
  search = "",
}: CompaniesGroupsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const { data, isLoading } = useQuery({
    queryKey: ["business-groups", currentPage, pageSize, search],
    queryFn: async () => {
      const res = await getBusinessGroups({
        page: currentPage,
        limit: pageSize,
        search: search || undefined,
      });
      const payload = (res as unknown as { data?: unknown }).data ?? res;
      return payload as {
        businessGroups?: Array<{
          id?: string;
          cnpj?: string;
          alias?: string;
          name?: string;
          contact?: { name?: string; email?: string; phone?: string };
        }>;
        total?: number;
        page?: number;
        limit?: number;
        totalPages?: number;
      };
    },
  });

  const rows: BusinessGroupRow[] = (data?.businessGroups || []).map((g) => ({
    id: String(g.id || ""),
    cnpj: String(g.cnpj || ""),
    alias: String(g.alias || ""),
    name: String(g.name || ""),
    contact: {
      name: String(g.contact?.name || ""),
      email: String(g.contact?.email || ""),
      phone: String(g.contact?.phone || ""),
    },
  }));

  const totalPages = data?.totalPages || 0;

  const columns: Column<BusinessGroupRow>[] = [
    { header: "CNPJ", render: (r) => formatCNPJ(r.cnpj) },
    { header: "Nome fantasia", render: (r) => r.alias },
    { header: "Nome do contato", render: (r) => r.contact.name },
    { header: "Email do contato", render: (r) => r.contact.email },
    {
      header: "Telefone do contato",
      render: (r) => maskPhoneBR(r.contact.phone),
    },
    {
      isActionColumn: true,
      header: "",
      render: (row) => (
        <CompaniesGroupModalForm
          isEditMode
          id={row.id}
          prefill={{
            cnpj: row.cnpj,
            alias: row.alias,
            name: row.name,
            contact: {
              name: row.contact.name,
              email: row.contact.email,
              phone: row.contact.phone,
            },
          }}
          triggerButton={
            <Button className="w-7 h-7">
              <Edit2 size={16} className="w-4 h-4" />
            </Button>
          }
        />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={rows}
      keyExtractor={(item) => item.id}
      className="w-full p-8"
      isLoading={isLoading}
      pagination={{
        currentPage,
        totalPages,
        onPageChange: setCurrentPage,
        pageSize,
        onPageSizeChange: (newSize) => {
          setPageSize(newSize);
          setCurrentPage(1);
        },
      }}
    />
  );
}
