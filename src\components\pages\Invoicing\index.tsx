import { Plus, Receipt } from "lucide-react";

import { BatchesList } from "./batchesList";
import { But<PERSON> } from "@/components/ui/button";
import { InvoicingModalForm } from "./formModal";

export function InvoicingPage() {
  return (
    <div className="w-full">
      <header className="w-full h-16 bg-[#256caf16] text-primary font-bold flex items-center justify-between p-6">
        <div className="flex gap-1">
          <Receipt size={20} absoluteStrokeWidth />
          <span>Faturamento</span>
        </div>

        <InvoicingModalForm
          triggerButton={
            <Button variant="secondary" className="h-10 px-8">
              <Plus size={20} />
              Novo faturamento
            </Button>
          }
        />
      </header>
      <div className="w-full">
        <BatchesList />
      </div>
    </div>
  );
}
