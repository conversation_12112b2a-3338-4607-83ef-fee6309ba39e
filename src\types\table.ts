export interface Column<T> {
  header: React.ReactNode;
  accessor?: keyof T;
  render?: (data: T) => React.ReactNode;
  isActionColumn?: boolean;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  pageSize: number;
  onPageSizeChange: (size: number) => void;
}

export interface TableProps<T> {
  columns: Column<T>[];
  data: T[];
  keyExtractor: (item: T) => string | number;
  className?: string;
  pagination?: PaginationProps;
  openFilterModal?: React.ReactNode;
  isLoading?: boolean;
  firstColumnClassName?: string;
}
