"use client";

import { cn } from "@/lib/utils";

type StatusBadgeProps =
  | {
      label?: string;
      variant: "success" | "error" | "warning" | "neutral";
      className?: string;
    }
  | {
      isActive: boolean; // backward compat
      className?: string;
      label?: never;
      variant?: never;
    };

export function StatusBadge(props: StatusBadgeProps) {
  const baseClass =
    "inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-medium";

  const isCompat = (p: StatusBadgeProps): p is { isActive: boolean } =>
    Object.prototype.hasOwnProperty.call(p, "isActive");

  let label = "";
  let colorClass = "";

  if (isCompat(props)) {
    label = props.isActive ? "Ativo" : "Inativo";
    colorClass = props.isActive
      ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-500/15 dark:text-emerald-400"
      : "bg-red-100 text-red-700 dark:bg-red-500/15 dark:text-red-400";
  } else {
    label = props.label ?? "";
    const map: Record<string, string> = {
      success:
        "bg-emerald-100 text-emerald-700 dark:bg-emerald-500/15 dark:text-emerald-400",
      error:
        "bg-red-100 text-red-700 dark:bg-red-500/15 dark:text-red-400",
      warning:
        "bg-amber-100 text-amber-700 dark:bg-amber-500/15 dark:text-amber-400",
      neutral:
        "bg-gray-100 text-gray-600 dark:bg-gray-500/15 dark:text-gray-300",
    };
    colorClass = map[props.variant];
  }

  return <span className={cn(baseClass, colorClass, props.className)}>{label}</span>;
}
