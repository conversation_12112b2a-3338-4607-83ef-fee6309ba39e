"use client";

import { Modal } from "@/components/modals";
import { But<PERSON> } from "@/components/ui/button";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { FirstStep } from "./firstStep";
import { SecondStep } from "./secondStep";
import {
  companiesGroupsValidation,
  companiesGroupsValidationType,
} from "./validations";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { SuccessModal } from "@/components/modals/success-modal";
import {
  createBusinessGroup,
  updateBusinessGroup,
  getBusinessGroupById,
} from "@/http/business-groups";
import { maskCEP } from "@/lib/format";

type SindicatoModalFormProps = {
  isEditMode?: boolean;
  triggerButton: React.ReactNode;
  id?: string;
  prefill?: {
    cnpj?: string;
    alias?: string;
    name?: string;
    address?: string;
    contact?: { name?: string; phone?: string; email?: string };
    financeContact?: { name?: string; phone?: string; email?: string };
  };
};

export function CompaniesGroupModalForm({
  triggerButton,
  isEditMode,
  id,
  prefill,
}: SindicatoModalFormProps) {
  const [step, setStep] = useState<number>(1);
  const [isOpen, setIsOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successTitle, setSuccessTitle] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const form = useForm<companiesGroupsValidationType>({
    resolver: zodResolver(companiesGroupsValidation),
    defaultValues:
      isEditMode && prefill
        ? {
            cnpj: prefill.cnpj,
            fantasyName: prefill.alias,
            socialReason: prefill.name,
            address: prefill.address,
            contact: prefill.contact
              ? {
                  name: prefill.contact.name || "",
                  phone: prefill.contact.phone || "",
                  email: prefill.contact.email || "",
                }
              : undefined,
            finance: prefill.financeContact
              ? {
                  name: prefill.financeContact.name || undefined,
                  phone: prefill.financeContact.phone || undefined,
                  email: prefill.financeContact.email || undefined,
                }
              : undefined,
          }
        : undefined,
  });

  async function hydrateEdit() {
    if (!isEditMode || !id) return;
    try {
      const res = await getBusinessGroupById({ id });
      const full = (res as unknown as { data?: unknown }).data ?? res;
      const entity = full as unknown as {
        cnpj?: string;
        alias?: string;
        name?: string;
        address?:
          | {
              street?: string;
              number?: string;
              complement?: string;
              zip?: string;
              neighborhood?: string;
              city?: string;
              state?: string;
            }
          | string;
        cnae?: string;
        union?: { id?: string; alias?: string };
        consultancy?: { id?: string; alias?: string };
        contact?: { name?: string; phone?: string; email?: string };
        financeContact?: { name?: string; phone?: string; email?: string };
      };

      const addrObj =
        (entity.address && typeof entity.address === "object"
          ? entity.address
          : undefined) || undefined;
      const addrParts: string[] = [];
      if (addrObj?.street && addrObj?.number) {
        addrParts.push(`${addrObj.street}, ${addrObj.number}`);
      }
      if (addrObj?.complement) addrParts.push(addrObj.complement);
      if (addrObj?.neighborhood) addrParts.push(addrObj.neighborhood);
      const cityState = [addrObj?.city, addrObj?.state]
        .filter(Boolean)
        .join(", ");
      if (cityState) addrParts.push(cityState);
      if (addrObj?.zip) addrParts.push(maskCEP(addrObj.zip));
      const addressText = addrParts.join(" | ");

      form.reset({
        union: entity.union?.id
          ? {
              label: String(entity.union?.alias || ""),
              value: String(entity.union.id),
            }
          : undefined,
        consultancy: entity.consultancy?.id
          ? {
              label: String(entity.consultancy?.alias || ""),
              value: String(entity.consultancy.id),
            }
          : undefined,
        cnpj: String(entity.cnpj || ""),
        fantasyName: String(entity.alias || ""),
        socialReason: String(entity.name || ""),
        address:
          addressText ||
          (typeof entity.address === "string" ? entity.address : ""),
        addressObj: addrObj,
        cnae: String(entity.cnae || ""),
        contact: {
          name: String(entity.contact?.name || ""),
          phone: String(entity.contact?.phone || ""),
          email: String(entity.contact?.email || ""),
        },
        finance: entity.financeContact
          ? {
              name: String(entity.financeContact?.name || ""),
              phone: String(entity.financeContact?.phone || ""),
              email: String(entity.financeContact?.email || ""),
            }
          : undefined,
      } as unknown as companiesGroupsValidationType);
    } catch {}
  }

  const handleNext = async () => {
    if (step === 1) {
      // Valida somente os campos da etapa 1
      const isValid = await form.trigger(
        ["union", "cnpj", "fantasyName", "socialReason", "address"],
        { shouldFocus: true }
      );
      if (!isValid) return;
      form.clearErrors(["contact", "finance"]);
      setStep(2);
      return;
    }
  };

  const handleSubmit = async (data: companiesGroupsValidationType) => {
    try {
      const unionId = (data as unknown as { union?: { value?: string } })?.union
        ?.value;
      const consultancyId = (
        data as unknown as { consultancy?: { value?: string } }
      )?.consultancy?.value;

      const financeRaw = (
        data as unknown as {
          finance?: { name?: string; phone?: string; email?: string };
        }
      ).finance;
      const financeNormalized =
        financeRaw && (financeRaw.name || financeRaw.phone || financeRaw.email)
          ? {
              name: financeRaw.name!,
              phone: financeRaw.phone!,
              email: financeRaw.email!,
            }
          : undefined;

      if (isEditMode && id) {
        await updateBusinessGroup({
          id,
          alias: data.fantasyName,
          contact: (
            data as unknown as {
              contact: { name: string; phone: string; email: string };
            }
          ).contact,
          financeContact: financeNormalized,
        });
        setSuccessTitle("Grupo empresarial atualizado com sucesso!");
      } else {
        const addr = (
          data as unknown as {
            addressObj?: {
              street?: string;
              number?: string;
              complement?: string;
              zip?: string;
              neighborhood?: string;
              city?: string;
              state?: string;
            };
          }
        ).addressObj;

        await createBusinessGroup({
          unionId: String(unionId || ""),
          consultancyId: consultancyId ? String(consultancyId) : undefined,
          cnpj: String(data.cnpj),
          alias: data.fantasyName,
          name: data.socialReason,
          address: {
            street: addr?.street || "",
            number: addr?.number || "",
            complement: addr?.complement || "",
            zip: addr?.zip || "",
            neighborhood: addr?.neighborhood || "",
            city: addr?.city || "",
            state: addr?.state || "",
          },
          cnae: String((data as unknown as { cnae?: string }).cnae || ""),
          contact: (
            data as unknown as {
              contact: { name: string; phone: string; email: string };
            }
          ).contact,
          financeContact: financeNormalized,
        });
        setSuccessTitle("Grupo empresarial cadastrado com sucesso!");
      }

      queryClient.invalidateQueries({ queryKey: ["business-groups"] });
      setShowSuccessModal(true);
      setIsOpen(false);
      setStep(1);
      form.reset();
    } catch (error: unknown) {
      const { extractApiErrorMessage } = await import("@/lib/utils");
      toast({
        title: "Erro",
        description: extractApiErrorMessage(
          error,
          "Erro ao salvar grupo empresarial"
        ),
        variant: "destructive",
      });
    }
  };

  const handleConclude = async () => {
    // Valida apenas os campos da etapa 2 antes de concluir
    const isValid = await form.trigger(["contact", "finance"]);
    if (!isValid) return;
    await handleSubmit(form.getValues());
  };

  return (
    <>
      <Modal
        title={
          isEditMode ? "Editar grupo empresarial" : "Novo grupo empresarial"
        }
        description={`Etapa ${step} de 2`}
        triggerButton={triggerButton}
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (open) hydrateEdit();
          if (!open) {
            setStep(1);
            form.reset();
          }
        }}
      >
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-16"
            onSubmit={(e) => e.preventDefault()}
          >
            {step === 1 && <FirstStep isEditMode={isEditMode} />}
            {step === 2 && <SecondStep />}
            <div className="flex gap-4">
              {step !== 1 && (
                <Button
                  variant="outline"
                  className="w-full font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
                  onClick={() => setStep(step - 1)}
                >
                  Voltar
                </Button>
              )}
              {step < 2 ? (
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleNext}
                  disabled={
                    step === 1 &&
                    (!!form.formState.errors.cnpj ||
                      (!isEditMode &&
                        (form.watch("cnpj") || "").replace(/\D/g, "").length <
                          14))
                  }
                >
                  Continuar
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleConclude}
                >
                  Concluir
                </Button>
              )}
            </div>
          </form>
        </FormProvider>
      </Modal>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={successTitle}
      />
    </>
  );
}
