export type InvoiceRow = {
  id: string;
  union: string;
  group: string;
  unit: string;
  lives: number;
  amount: string;
  dueDate: string;
  generatedAt: string;
  paymentStatus: "Pendente" | "Pago" | "Atrasado" | "Cancelado";
  generationStatus: "Sucesso" | "Erro" | "Pendente";
};

export const mockedInvoices: InvoiceRow[] = Array.from({ length: 10 }).map(
  (_, idx) => ({
    id: String(idx + 1),
    union: "Lorem ipsum",
    group: "Lorem ipsum",
    unit: "Lorem ipsum",
    lives: 0,
    amount: "R$0,00",
    dueDate: "dd/mm/aaaa",
    generatedAt: "dd/mm/aaaa",
    paymentStatus: "Pendente",
    generationStatus: idx > 7 ? "Erro" : "Sucesso",
  })
);
