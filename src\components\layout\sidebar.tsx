import {
  Building2,
  Calculator,
  Receipt,
  Store,
  UserCog,
  Users,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Image from "next/image";
import Link from "next/link";

const items = [
  {
    title: "Grupos Empresariais",
    url: "/grupos-empresariais",
    icon: Building2,
  },
  {
    title: "Unidades",
    url: "/gestao-de-unidades",
    icon: Store,
  },
  {
    title: "Contabilidades",
    url: "/contabilidades",
    icon: Calculator,
  },
  {
    title: "Sindicatos",
    url: "/sindicatos",
    icon: Users,
  },
  {
    title: "Usuários",
    url: "/usuarios",
    icon: UserCog,
  },
  {
    title: "Faturamento",
    url: "/faturamento",
    icon: Receipt,
  },
];

export function AppSidebar() {
  return (
    <Sidebar title="Sidebar">
      <SidebarContent className="bg-primary py-6">
        <SidebarGroup className="flex flex-col items-center gap-10 w-full">
          <SidebarGroupLabel>
            <div className="relative w-24 h-10">
              <Image
                src="/saudeDaGente_white.png"
                alt="Logo"
                fill
                objectFit="contain"
                className="absolute"
              />
            </div>
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="flex items-center text-center gap-5">
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-5 hover:text-primary hover:font-bold"
                  >
                    <Link href={item.url} className="text-white">
                      <item.icon strokeWidth={2} />
                      <span className="w-40">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
