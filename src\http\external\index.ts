import { api } from "@/services/api";

export interface ExternalCompanyData {
  cnpj: string;
  name: string;
  alias: string;
  status: string;
  address: {
    street: string;
    number: string;
    complement?: string;
    zip: string;
    neighborhood: string;
    city: string;
    state: string;
  };
  cnae: string;
}

export const getCompanyByCNPJ = async (
  cnpj: string
): Promise<ExternalCompanyData> => {
  const response = await api.get(`/api/external/company/${cnpj}`);
  return response.data;
};
