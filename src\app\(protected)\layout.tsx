import { Head<PERSON> } from "@/components/layout/header";
import { AppSidebar } from "@/components/layout/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AuthGuard } from "@/components/auth/auth-guard";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard>
      <SidebarProvider>
        <AppSidebar />
        <main className="w-full">
          {/* <SidebarTrigger className="absolute text-white" /> */}
          <Header />
          {children}
        </main>
      </SidebarProvider>
    </AuthGuard>
  );
}
