"use client";

import { Modal } from "@/components/modals";
import { But<PERSON> } from "@/components/ui/button";

import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { sindicatoValidation, sindicatoValidationType } from "./validations";
import { useState, useEffect } from "react";
import { FirstStep } from "./firstStep";
import { SecondStep } from "./secondStep";
import { ThirdStep } from "./thirdStep";
import { Union, createUnion, updateUnion, getUnionById } from "@/http/unions";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { getCompanyByCNPJ } from "@/http/external";
import { unformatCNPJ, maskCpfCnpj } from "@/lib/format";
import { useToast } from "@/hooks/use-toast";
import { SuccessModal } from "@/components/modals/success-modal";

type SindicatoModalFormProps = {
  isEditMode?: boolean;
  triggerButton: React.ReactNode;
  union?: Union;
};

export function SindicatoModalForm({
  triggerButton,
  isEditMode,
  union,
}: SindicatoModalFormProps) {
  const [step, setStep] = useState<number>(1);
  const [isOpen, setIsOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successTitle, setSuccessTitle] = useState<string>("");
  const [isFetchingCompany, setIsFetchingCompany] = useState(false);
  const [isHydratingEdit, setIsHydratingEdit] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<sindicatoValidationType>({
    resolver: zodResolver(sindicatoValidation),
    defaultValues:
      isEditMode && union
        ? {
            cnpj: union.cnpj,
            cpfCnpj: maskCpfCnpj(union.cpfCnpj),
            alias: union.alias,
            name: union.name,
            registrationStatus: union.registrationStatus,
            address: union.address,
            bankId: union.bank.id,
            agency: union.agency,
            accountNumber: union.accountNumber,
            pixKey: union.pixKey,
            commissionPercentage: union.commissionPercentage,
            commissionPaymentDay: union.commissionPaymentDay,
            contact: union.contact,
            financeContact: union.financeContact,
            plans: union.plans,
          }
        : {
            plans: [],
          },
  });

  const { toast } = useToast();

  const createMutation = useMutation({
    mutationFn: createUnion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["unions"] });
      form.reset();
      setStep(1);
      setIsOpen(false);
      setSuccessTitle("Sindicato cadastrado com sucesso!");
      setShowSuccessModal(true);
    },
    onError: (error: { response?: { data?: { message?: string } } }) => {
      toast({
        title: "Erro",
        description: error.response?.data?.message || "Erro ao criar sindicato",
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: sindicatoValidationType) => {
      const financeContactNormalized =
        data.financeContact &&
        data.financeContact.name &&
        data.financeContact.phone &&
        data.financeContact.email
          ? {
              name: data.financeContact.name,
              phone: data.financeContact.phone,
              email: data.financeContact.email,
            }
          : undefined;

      const payload: {
        alias?: string;
        contact?: { name: string; phone: string; email: string };
        financeContact?: { name: string; phone: string; email: string };
        bankId?: string;
        agency?: string;
        accountNumber?: string;
        pixKey?: string;
        commissionPercentage?: number;
        commissionPaymentDay?: number;
        plans?: Array<{ id: string; value: number }>;
      } = {
        alias: data.alias,
        contact: data.contact,
        bankId: data.bankId,
        agency: data.agency,
        accountNumber: data.accountNumber,
        pixKey: data.pixKey,
        commissionPercentage: data.commissionPercentage,
        commissionPaymentDay: data.commissionPaymentDay,
        plans: data.plans,
      };

      if (financeContactNormalized) {
        payload.financeContact = financeContactNormalized;
      }

      return updateUnion(union!.id, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["unions"] });
      setIsOpen(false);
      setSuccessTitle("Sindicato atualizado com sucesso!");
      setShowSuccessModal(true);
    },
    onError: (error: { response?: { data?: { message?: string } } }) => {
      toast({
        title: "Erro",
        description:
          error.response?.data?.message || "Erro ao atualizar sindicato",
        variant: "destructive",
      });
    },
  });

  const isLastStep = step === 3;

  // Validações específicas por etapa
  const getStepErrors = (): boolean => {
    const errors = form.formState.errors;
    const values = form.getValues();
    const hasCompanyData = Boolean(values.name && values.address);

    switch (step) {
      case 1:
        // Antes de carregar os dados da empresa, só validamos CNPJ.
        // Depois de carregado, validamos também o alias.
        return !!(errors.cnpj || (hasCompanyData && errors.alias));
      case 2:
        return !!(
          errors.cpfCnpj ||
          errors.bankId ||
          errors.agency ||
          errors.accountNumber ||
          errors.pixKey ||
          errors.commissionPercentage ||
          errors.commissionPaymentDay
        );
      case 3:
        return !!(errors.contact || errors.financeContact);
      default:
        return false;
    }
  };

  const hasCurrentStepErrors = getStepErrors();

  // Quando abrir em modo de edição sem dados completos, buscar por ID e hidratar o formulário
  useEffect(() => {
    let cancelled = false;
    (async () => {
      if (!isOpen || !isEditMode || !union?.id) return;
      try {
        setIsHydratingEdit(true);
        const full = await getUnionById(union.id);
        if (cancelled) return;
        form.reset({
          cnpj: full.cnpj,
          cpfCnpj: maskCpfCnpj(full.cpfCnpj),
          alias: full.alias,
          name: full.name,
          registrationStatus: full.registrationStatus,
          address: full.address,
          bankId: full.bank.id,
          agency: full.agency,
          accountNumber: full.accountNumber,
          pixKey: full.pixKey,
          commissionPercentage: full.commissionPercentage,
          commissionPaymentDay: full.commissionPaymentDay,
          contact: full.contact,
          financeContact: (full.financeContact && {
            name: full.financeContact.name,
            phone: full.financeContact.phone,
            email: full.financeContact.email,
          }) as unknown as
            | { name?: string; phone?: string; email?: string }
            | undefined,
          plans: (
            (full.plans as Array<{
              id: string;
              value: number;
            }>) || []
          ).map((p) => ({ id: p.id, value: p.value })),
        });
      } catch {
        // silêncio: erros ignorados ao hidratar
      } finally {
        setIsHydratingEdit(false);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [isOpen, isEditMode, union?.id, form]);

  const handleSubmitSindicato = async (data: sindicatoValidationType) => {
    // Etapa 1: Se ainda não carregamos a empresa, tentamos buscar pelo CNPJ
    if (step === 1 && (!data.name || !data.address)) {
      const cleanCnpj = unformatCNPJ(data.cnpj);
      try {
        setIsFetchingCompany(true);
        const company = await getCompanyByCNPJ(cleanCnpj);
        form.setValue("name", company.name);
        form.setValue("alias", company.alias);
        form.setValue("registrationStatus", company.status);
        form.setValue("cnae", company.cnae);
        form.setValue("address", company.address);
        return; // permanece na etapa 1 exibindo dados carregados
      } catch (error: unknown) {
        const err = error as { response?: { data?: { message?: string } } };
        toast({
          title: "Erro",
          description:
            err?.response?.data?.message || "Erro ao buscar dados da empresa",
          variant: "destructive",
        });
        return;
      } finally {
        setIsFetchingCompany(false);
      }
    }

    if (hasCurrentStepErrors && !isLastStep) {
      return;
    }

    if (!isLastStep) {
      setStep((state) => state + 1);
      return;
    }

    if (isEditMode && union) {
      updateMutation.mutate(data);
    } else {
      const financeContactNormalized =
        data.financeContact &&
        data.financeContact.name &&
        data.financeContact.phone &&
        data.financeContact.email
          ? {
              name: data.financeContact.name,
              phone: data.financeContact.phone,
              email: data.financeContact.email,
            }
          : undefined;

      const payload = {
        cnpj: data.cnpj,
        registrationStatus: data.registrationStatus,
        cpfCnpj: (data.cpfCnpj || "").replace(/\D/g, ""),
        alias: data.alias,
        name: data.name,
        address: data.address,
        contact: data.contact,
        financeContact: financeContactNormalized,
        bankId: data.bankId,
        agency: data.agency,
        accountNumber: data.accountNumber,
        pixKey: data.pixKey,
        commissionPercentage: data.commissionPercentage,
        commissionPaymentDay: data.commissionPaymentDay,
        plans: data.plans,
      } as const;
      createMutation.mutate(payload);
    }
  };

  const handleContinue = async () => {
    const values = form.getValues();
    const hasCompanyData = Boolean(values.name && values.address);

    if (step === 1) {
      // Valida CNPJ e alias (alias obrigatório após busca)
      const fieldsToValidate: (keyof sindicatoValidationType)[] = [
        "cnpj",
        "alias",
      ];
      const isValid = await form.trigger(fieldsToValidate);
      if (!isValid) return;
      if (!hasCompanyData) return; // exige que já tenha sido feita a busca
      setStep(2);
      return;
    }

    if (step === 2) {
      const isValid = await form.trigger([
        "cpfCnpj",
        "bankId",
        "agency",
        "accountNumber",
        "pixKey",
        "commissionPercentage",
        "commissionPaymentDay",
        "plans",
      ]);
      if (!isValid) return;
      setStep(3);
      return;
    }

    if (step === 3) {
      // Valida contatos e envia
      const isValid = await form.trigger(["contact", "financeContact"]);
      if (!isValid) return;
      await handleSubmitSindicato(form.getValues());
    }
  };

  return (
    <>
      <Modal
        title={isEditMode ? "Editar sindicato" : "Novo sindicato"}
        description={
          step === 1
            ? "Etapa 1 de 3 - Identificação"
            : step === 2
            ? "Etapa 2 de 3 - Financeiro"
            : "Etapa 3 de 3 - Contatos"
        }
        triggerButton={triggerButton}
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) {
            // Ao fechar o modal, reinicia o fluxo
            form.reset();
            setStep(1);
            setIsFetchingCompany(false);
          }
        }}
      >
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-16"
            onSubmit={(e) => e.preventDefault()}
          >
            {step === 1 && (
              <FirstStep
                isLoading={isFetchingCompany || isHydratingEdit}
                isEditMode={isEditMode}
              />
            )}
            {step === 2 && <SecondStep />}
            {step === 3 && <ThirdStep />}
            <div className="flex gap-4">
              {(step !== 1 || (!!form.getValues().name && !isEditMode)) && (
                <Button
                  type="button"
                  variant="outline"
                  className="w-full font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
                  onClick={() => {
                    if (step > 1) {
                      setStep(step - 1);
                      return;
                    }
                    // step === 1 e somente criação: limpar dados da empresa e manter no passo 1
                    const values = form.getValues();
                    if (values.name || values.address) {
                      form.setValue("alias", "");
                      form.setValue("name", "");
                      form.setValue("registrationStatus", "");
                      form.setValue("cnae", "");
                      form.setValue("address", {
                        street: "",
                        number: "",
                        complement: "",
                        zip: "",
                        neighborhood: "",
                        city: "",
                        state: "",
                      } as unknown as typeof values.address);
                    }
                  }}
                  disabled={
                    createMutation.isPending || updateMutation.isPending
                  }
                >
                  Voltar
                </Button>
              )}
              <Button
                type="button"
                variant="secondary"
                className="w-full"
                disabled={
                  (step === 1 &&
                    (isFetchingCompany ||
                      !!form.formState.errors.cnpj ||
                      (!isEditMode &&
                        (form.watch("cnpj") || "").replace(/\D/g, "").length <
                          14))) ||
                  createMutation.isPending ||
                  updateMutation.isPending
                }
                onClick={handleContinue}
              >
                {isFetchingCompany
                  ? "Buscando..."
                  : createMutation.isPending || updateMutation.isPending
                  ? "Salvando..."
                  : isLastStep
                  ? "Concluir"
                  : "Continuar"}
              </Button>
            </div>
          </form>
        </FormProvider>
      </Modal>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={successTitle}
      />
    </>
  );
}
