"use client";

import { Modal } from "@/components/modals";
import { CheckboxList } from "@/components/ui/filterCheckbox";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { translateUserRole } from "@/lib/format";
import { useQuery } from "@tanstack/react-query";
import { getConsultancies } from "@/http/consultancies";
import { getUnions } from "@/http/unions";
import { getBusinessGroups } from "@/http/business-groups";
import { getBusinessUnits } from "@/http/business-units";

type UserModalFormProps = {
  triggerButton: React.ReactNode;
  onApply?: (filters: {
    consultancyIds: string[];
    unionIds: string[];
    businessGroupIds: string[];
    businessUnitIds: string[];
    roles?: string[];
    isActive?: boolean;
  }) => void;
};

//

export function FilterUserModal({
  triggerButton,
  onApply,
}: UserModalFormProps) {
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedConsultancies, setSelectedConsultancies] = useState<string[]>(
    []
  );
  const [selectedUnions, setSelectedUnions] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [searchConsultancies, setSearchConsultancies] = useState("");
  const [searchUnions, setSearchUnions] = useState("");
  const [searchGroups, setSearchGroups] = useState("");
  const [searchUnits, setSearchUnits] = useState("");

  const { data: consultanciesData, isLoading: loadingConsultancies } = useQuery(
    {
      queryKey: ["consultancies", { search: searchConsultancies }],
      queryFn: async () => {
        const res = await getConsultancies({
          page: 1,
          limit: 25,
          search: searchConsultancies,
        });
        return res.data;
      },
    }
  );
  type IdName = { id: string; name?: string; alias?: string };
  const consultancyOptions = (
    (consultanciesData?.consultancies || []) as IdName[]
  ).map((c) => ({ label: c.name || "", value: c.id }));

  const { data: unionsData, isLoading: loadingUnions } = useQuery({
    queryKey: ["unions", { search: searchUnions }],
    queryFn: () => getUnions({ page: 1, limit: 25, search: searchUnions }),
  });
  const unionOptions = ((unionsData?.unions || []) as IdName[]).map((u) => ({
    label: u.alias || u.name || "",
    value: u.id,
  }));

  const { data: groupsResp, isLoading: loadingGroups } = useQuery({
    queryKey: ["business-groups", { search: searchGroups }],
    queryFn: async () => {
      const res = await getBusinessGroups({
        page: 1,
        limit: 25,
        search: searchGroups,
      });
      return (
        (res as unknown as { data?: { businessGroups?: IdName[] } }).data ||
        (res as unknown as { businessGroups?: IdName[] })
      );
    },
  });
  const groupOptions = ((groupsResp?.businessGroups || []) as IdName[]).map(
    (g) => ({
      label: g.alias || g.name || "",
      value: g.id,
    })
  );

  const { data: unitsResp, isLoading: loadingUnits } = useQuery({
    queryKey: ["business-units", { search: searchUnits }],
    queryFn: async () => {
      const res = await getBusinessUnits({
        page: 1,
        limit: 25,
        search: searchUnits,
      });
      return (
        (res as unknown as { data?: { businessUnits?: IdName[] } }).data ||
        (res as unknown as { businessUnits?: IdName[] })
      );
    },
  });
  const unitOptions = ((unitsResp?.businessUnits || []) as IdName[]).map(
    (u) => ({
      label: u.alias || u.name || "",
      value: u.id,
    })
  );

  const roleOptions = [
    { value: "ADMIN_CLIENT", label: translateUserRole("ADMIN_CLIENT") },
    { value: "ADMIN_SDG", label: translateUserRole("ADMIN_SDG") },
    { value: "ATTENDANT_SDG", label: translateUserRole("ATTENDANT_SDG") },
    { value: "ACCOUNTANT", label: translateUserRole("ACCOUNTANT") },
    { value: "CONTROLLER_SDG", label: translateUserRole("CONTROLLER_SDG") },
    { value: "FINANCIAL_CLIENT", label: translateUserRole("FINANCIAL_CLIENT") },
    { value: "FINANCIAL_SDG", label: translateUserRole("FINANCIAL_SDG") },
  ];

  const statusOptions = [
    { label: "Ativo", value: "active" },
    { label: "Inativo", value: "inactive" },
  ];

  const [isOpen, setIsOpen] = useState(false);
  return (
    <Modal
      title="Filtrar por"
      triggerButton={triggerButton}
      headerAlign="left"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <div className="flex flex-col gap-6">
        <CheckboxList
          title="Perfil"
          options={roleOptions}
          selectedValues={selectedRoles}
          onSelectionChange={setSelectedRoles}
          searchPlaceholder="Digite o nome do perfil"
          emptyMessage="Nenhum perfil encontrado"
          hideSearchBar
        />
        <Separator />
        <CheckboxList
          title="Contabilidades"
          options={consultancyOptions}
          selectedValues={selectedConsultancies}
          onSelectionChange={setSelectedConsultancies}
          searchPlaceholder="Digite o nome da contabilidade"
          searchValue={searchConsultancies}
          onSearchChange={setSearchConsultancies}
          isLoading={loadingConsultancies}
          emptyMessage="Nenhuma contabilidade encontrada"
        />
        <Separator />
        <CheckboxList
          title="Sindicatos"
          options={unionOptions}
          selectedValues={selectedUnions}
          onSelectionChange={setSelectedUnions}
          searchPlaceholder="Digite o nome do sindicato"
          searchValue={searchUnions}
          onSearchChange={setSearchUnions}
          isLoading={loadingUnions}
          emptyMessage="Nenhum sindicato encontrado"
        />
        <Separator />
        <CheckboxList
          title="Grupos"
          options={groupOptions}
          selectedValues={selectedGroups}
          onSelectionChange={setSelectedGroups}
          searchPlaceholder="Digite o nome do grupo"
          searchValue={searchGroups}
          onSearchChange={setSearchGroups}
          isLoading={loadingGroups}
          emptyMessage="Nenhum grupo encontrado"
        />
        <Separator />
        <CheckboxList
          title="Unidades"
          options={unitOptions}
          selectedValues={selectedUnits}
          onSelectionChange={setSelectedUnits}
          searchPlaceholder="Digite o nome da unidade"
          searchValue={searchUnits}
          onSearchChange={setSearchUnits}
          isLoading={loadingUnits}
          emptyMessage="Nenhuma unidade encontrada"
        />
        <Separator />
        <CheckboxList
          title="Status"
          options={statusOptions}
          selectedValues={selectedStatus}
          onSelectionChange={setSelectedStatus}
          hideSearchBar
        />
        <div className="flex w-full gap-3 pt-2">
          <Button
            variant="outline"
            className="flex-1 font-bold"
            onClick={() => {
              setSelectedConsultancies([]);
              setSelectedUnions([]);
              setSelectedGroups([]);
              setSelectedUnits([]);
              setSelectedRoles([]);
              setSearchConsultancies("");
              setSearchUnions("");
              setSearchGroups("");
              setSearchUnits("");
              setSelectedStatus([]);
            }}
          >
            Limpar todos
          </Button>
          <Button
            variant="secondary"
            className="flex-1 font-bold"
            onClick={() => {
              const isActive =
                selectedStatus.length === 1
                  ? selectedStatus[0] === "active"
                  : undefined;
              onApply?.({
                consultancyIds: selectedConsultancies,
                unionIds: selectedUnions,
                businessGroupIds: selectedGroups,
                businessUnitIds: selectedUnits,
                roles: selectedRoles,
                isActive,
              });
              setIsOpen(false);
            }}
          >
            Aplicar
          </Button>
        </div>
      </div>
    </Modal>
  );
}
