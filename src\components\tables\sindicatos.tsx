"use client";

import { useState } from "react";
import { DataTable } from "../ui/datatable";
import { Button } from "../ui/button";
import { Edit2 } from "lucide-react";
import { Column } from "@/types/table";
import { SindicatoModalForm } from "../pages/sindicatos/sindicatoModal";
import { useQuery } from "@tanstack/react-query";
import { getUnions } from "@/http/unions";
import { Union } from "@/http/unions/types";
import { useDebounce } from "@/hooks/use-debounce";
import { formatCNPJ, maskPhoneBR } from "@/lib/format";

type SindicatosTableProps = {
  search: string;
};

export function SindicatosTable({ search }: SindicatosTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const debouncedSearch = useDebounce(search, 500);

  const { data: unionsData, isLoading } = useQuery({
    queryKey: ["unions", currentPage, pageSize, debouncedSearch],
    queryFn: () =>
      getUnions({
        page: currentPage,
        limit: pageSize,
        search: debouncedSearch || undefined,
      }),
  });

  const unions = unionsData?.unions || [];
  const totalPages = unionsData?.totalPages || 0;

  const columns: Column<Union>[] = [
    {
      header: "CNPJ",
      render: (union) => formatCNPJ(union.cnpj),
    },
    {
      header: "Nome fantasia",
      render: (union) => union.alias,
    },
    {
      header: "Nome do contato",
      render: (union) => union.contact.name,
    },
    {
      header: "E-mail do contato",
      render: (union) => union.contact.email,
    },
    {
      header: "Telefone do contato",
      render: (union) => maskPhoneBR(union.contact.phone),
    },
    {
      header: "Dia de pagamento",
      render: (union) => union.commissionPaymentDay.toString(),
    },
    {
      isActionColumn: true,
      header: "",
      render: (union) => (
        <SindicatoModalForm
          isEditMode
          union={union}
          triggerButton={
            <Button className="w-7 h-7">
              <Edit2 size={16} className="w-4 h-4" />
            </Button>
          }
        />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={unions}
      keyExtractor={(item) => item.id}
      className="w-full p-8"
      isLoading={isLoading}
      pagination={{
        currentPage,
        totalPages,
        onPageChange: setCurrentPage,
        pageSize,
        onPageSizeChange: (newSize) => {
          setPageSize(newSize);
          setCurrentPage(1);
        },
      }}
    />
  );
}
