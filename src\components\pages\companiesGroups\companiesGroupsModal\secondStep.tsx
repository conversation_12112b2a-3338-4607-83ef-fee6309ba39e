import { Input } from "@/components/ui/input";
import { FieldErrors, useFormContext } from "react-hook-form";
import { maskPhoneBR } from "@/lib/format";
import { useEffect } from "react";

export function SecondStep() {
  const { register, formState, setValue, getValues } = useFormContext();

  const errors = formState.errors as FieldErrors<{
    contact: { name?: string; phone?: string; email?: string };
    finance?: { name?: string; phone?: string; email?: string };
  }>;

  useEffect(() => {
    const current = getValues("contact.phone");
    if (current) setValue("contact.phone", maskPhoneBR(String(current)));
    const fin = getValues("finance.phone");
    if (fin) setValue("finance.phone", maskPhoneBR(String(fin)));
  }, [getValues, setValue]);

  return (
    <div className="flex flex-col gap-4">
      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5">
        Contato principal
      </span>
      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome"
          {...register("contact.name")}
          errorMessage={(errors.contact?.name?.message as string) || undefined}
        />
        {(() => {
          const phoneReg = register("contact.phone");
          return (
            <Input
              label="Telefone"
              placeholder="Digite o telefone"
              name={phoneReg.name}
              ref={phoneReg.ref}
              onBlur={phoneReg.onBlur}
              onChange={(e) => {
                e.target.value = maskPhoneBR(e.target.value);
                phoneReg.onChange(e);
              }}
              onInput={(e) => {
                const el = e.target as HTMLInputElement;
                el.value = maskPhoneBR(el.value);
                phoneReg.onChange(
                  e as unknown as React.ChangeEvent<HTMLInputElement>
                );
              }}
              errorMessage={
                (errors.contact?.phone?.message as string) || undefined
              }
            />
          );
        })()}
        <Input
          label="Email"
          placeholder="Digite o email"
          {...register("contact.email")}
          errorMessage={(errors.contact?.email?.message as string) || undefined}
        />
      </div>

      <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-8">
        Contato financeiro
      </span>

      <div className="flex flex-col gap-6">
        <Input
          label="Nome"
          placeholder="Digite o nome"
          {...register("finance.name", {
            setValueAs: (v) =>
              typeof v === "string" && v.trim() === "" ? undefined : v,
          })}
          errorMessage={(errors.finance?.name?.message as string) || undefined}
        />
        {(() => {
          const financePhoneReg = register("finance.phone", {
            setValueAs: (v) =>
              typeof v === "string" && v.trim() === "" ? undefined : v,
          });
          return (
            <Input
              label="Telefone"
              placeholder="Digite o telefone"
              name={financePhoneReg.name}
              ref={financePhoneReg.ref}
              onBlur={financePhoneReg.onBlur}
              onChange={(e) => {
                e.target.value = maskPhoneBR(e.target.value);
                financePhoneReg.onChange(e);
              }}
              onInput={(e) => {
                const el = e.target as HTMLInputElement;
                el.value = maskPhoneBR(el.value);
                financePhoneReg.onChange(
                  e as unknown as React.ChangeEvent<HTMLInputElement>
                );
              }}
              errorMessage={
                (errors.finance?.phone?.message as string) || undefined
              }
            />
          );
        })()}
        <Input
          label="Email"
          placeholder="Digite o email"
          {...register("finance.email", {
            setValueAs: (v) =>
              typeof v === "string" && v.trim() === "" ? undefined : v,
          })}
          errorMessage={(errors.finance?.email?.message as string) || undefined}
        />
      </div>
    </div>
  );
}
