/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { Modal } from "@/components/modals";
import { But<PERSON> } from "@/components/ui/button";

import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { FirstStep } from "./firstStep";
import { SecondStep } from "./secondStep";
import {
  businessUnitValidation,
  BusinessUnitValidationType,
} from "./validations";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { getBusinessGroupById } from "@/http/business-groups";
import { getUnionById } from "@/http/unions";
import { createBusinessUnit, updateBusinessUnit } from "@/http/business-units";
import { useToast } from "@/hooks/use-toast";
import { SuccessModal } from "@/components/modals/success-modal";

type UnityModalFormProps = {
  isEditMode?: boolean;
  triggerButton: React.ReactNode;
  id?: string;
  prefill?: {
    cnpj?: string;
    alias?: string;
    name?: string;
    dueDay?: number;
    businessGroup?: { label: string; value: string };
  };
};

export function UnityModalForm({
  triggerButton,
  isEditMode,
  id,
  prefill,
}: UnityModalFormProps) {
  const [step, setStep] = useState<number>(1);
  const [isOpen, setIsOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const form = useForm<BusinessUnitValidationType>({
    resolver: zodResolver(businessUnitValidation),
    defaultValues:
      isEditMode && prefill
        ? {
            cnpj: prefill.cnpj,
            fantasyName: prefill.alias,
            socialReason: prefill.name,
            dueDay: prefill.dueDay,
            businessGroup: prefill.businessGroup,
            plans: [],
          }
        : { plans: [] },
  });

  const isLastStep = step === 2;

  const createMutation = useMutation({
    mutationFn: createBusinessUnit,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["business-units"] });
      setIsOpen(false);
      setShowSuccessModal(true);
      setStep(1);
      form.reset({ plans: [] });
    },
    onError: async (error: unknown) => {
      const { extractApiErrorMessage } = await import("@/lib/utils");
      toast({
        title: "Erro",
        description: extractApiErrorMessage(error, "Erro ao salvar unidade"),
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: updateBusinessUnit,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["business-units"] });
      setIsOpen(false);
      setShowSuccessModal(true);
      setStep(1);
      form.reset({ plans: [] });
    },
    onError: async (error: unknown) => {
      const { extractApiErrorMessage } = await import("@/lib/utils");
      toast({
        title: "Erro",
        description: extractApiErrorMessage(error, "Erro ao atualizar unidade"),
        variant: "destructive",
      });
    },
  });

  // Quando escolher o grupo no passo 1 e o modal estiver aberto, buscar o sindicato e seus planos
  useEffect(() => {
    let cancelled = false;
    (async () => {
      // Em modo de edição, não sobrescrever os planos já carregados do detalhe
      if (!isOpen || isEditMode) return;
      const group = (
        form.getValues() as unknown as { businessGroup?: { value?: string } }
      ).businessGroup;
      const businessGroupId = group?.value;
      if (!businessGroupId) return;
      try {
        const res = await getBusinessGroupById({ id: String(businessGroupId) });
        const full = (res as unknown as { data?: unknown }).data ?? res;
        const entity = full as unknown as { union?: { id?: string } };
        const unionId = entity.union?.id;
        if (!unionId) return;
        const union = await getUnionById(String(unionId));
        if (cancelled) return;
        // Preenche o array de planos com employeeCount = 0
        const plans = (union.plans || []).map((p) => ({
          planId: p.id,
          employeeCount: 0,
        }));
        form.setValue(
          "plans",
          plans as unknown as BusinessUnitValidationType["plans"]
        );
        // Guarda planos do sindicato para exibir nomes no passo 2 (campo fora do schema)
        (form.setValue as unknown as (k: string, v: unknown) => void)(
          "unionPlans",
          union.plans as unknown as Array<{ id: string; name: string }>
        );
      } catch {}
    })();
    return () => {
      cancelled = true;
    };
  }, [isOpen, form.watch("businessGroup")]);

  // Hidratar quando abrir em modo edição
  useEffect(() => {
    let cancelled = false;
    (async () => {
      if (!isOpen || !isEditMode || !id) return;
      try {
        const { getBusinessUnitById } = await import("@/http/business-units");
        const res = (await getBusinessUnitById({ id })) as unknown as
          | { data?: unknown }
          | Record<string, unknown>;
        const full =
          (res as { data?: unknown }).data ?? (res as Record<string, unknown>);
        if (cancelled) return;

        const unit = full as unknown as {
          cnpj?: string;
          alias?: string;
          name?: string;
          address?: {
            street?: string;
            number?: string;
            complement?: string;
            zip?: string;
            neighborhood?: string;
            city?: string;
            state?: string;
          };
          cnae?: string;
          dueDay?: number;
          businessGroup?: { id?: string; alias?: string; name?: string };
          businessUnitPlans?: Array<{
            plan?: { id: string; name: string };
            employeeCount?: number;
          }>;
        };

        const normalizedPlans = (unit.businessUnitPlans || []).map((bp) => ({
          planId: String(
            // aceita `plan.id` (objeto) ou `planId` direto
            (bp as unknown as { planId?: string })?.planId || bp.plan?.id || ""
          ),
          employeeCount: Number(bp.employeeCount || 0),
        }));

        form.reset({
          cnpj: unit.cnpj,
          fantasyName: unit.alias,
          socialReason: unit.name,
          addressObj: unit.address as unknown as {
            street?: string;
            number?: string;
            complement?: string;
            zip?: string;
            neighborhood?: string;
            city?: string;
            state?: string;
          },
          cnae: unit.cnae as unknown as string,
          dueDay: unit.dueDay as unknown as number,
          businessGroup: unit.businessGroup?.id
            ? {
                value: String(unit.businessGroup.id),
                label: String(
                  unit.businessGroup.alias || unit.businessGroup.name || ""
                ),
              }
            : undefined,
          plans: normalizedPlans,
        } as BusinessUnitValidationType);

        // nomes dos planos para exibição
        (form.setValue as unknown as (k: string, v: unknown) => void)(
          "unionPlans",
          (unit.businessUnitPlans || []).map((bp) => ({
            id: String(
              (bp as unknown as { planId?: string })?.planId ||
                bp.plan?.id ||
                ""
            ),
            name: String(
              (bp as unknown as { name?: string })?.name || bp.plan?.name || ""
            ),
          }))
        );
      } catch {}
    })();
    return () => {
      cancelled = true;
    };
  }, [isOpen, isEditMode, id, form]);

  const handleContinue = async () => {
    if (step === 1) {
      const isValid = await form.trigger(["businessGroup", "cnpj"]);
      if (!isValid) return;
      setStep(2);
      return;
    }
  };

  const handleConclude = async () => {
    const isValid = await form.trigger(["dueDay", "plans"]);
    if (!isValid) return;
    const values = form.getValues();
    const group = (values as unknown as { businessGroup?: { value?: string } })
      .businessGroup;
    const businessGroupId = String(group?.value || "");
    if (isEditMode && id) {
      updateMutation.mutate({
        id,
        alias: String(values.fantasyName || ""),
        name: String(values.socialReason || ""),
        address:
          (values.addressObj as unknown as {
            street: string;
            number: string;
            complement?: string;
            zip: string;
            neighborhood: string;
            city: string;
            state: string;
          }) ||
          ({
            street: "",
            number: "",
            zip: "",
            neighborhood: "",
            city: "",
            state: "",
          } as unknown as {
            street: string;
            number: string;
            complement?: string;
            zip: string;
            neighborhood: string;
            city: string;
            state: string;
          }),
        cnae: String((values as unknown as { cnae?: string }).cnae || ""),
        dueDay: Number(values.dueDay),
        plans: values.plans,
      });
    } else {
      createMutation.mutate({
        businessGroupId,
        cnpj: String(values.cnpj || ""),
        alias: String(values.fantasyName || ""),
        name: String(values.socialReason || ""),
        address:
          (values.addressObj as unknown as {
            street: string;
            number: string;
            complement?: string;
            zip: string;
            neighborhood: string;
            city: string;
            state: string;
          }) ||
          ({
            street: "",
            number: "",
            zip: "",
            neighborhood: "",
            city: "",
            state: "",
          } as unknown as {
            street: string;
            number: string;
            complement?: string;
            zip: string;
            neighborhood: string;
            city: string;
            state: string;
          }),
        cnae: String((values as unknown as { cnae?: string }).cnae || ""),
        dueDay: Number(values.dueDay),
        plans: values.plans,
      });
    }
  };

  return (
    <>
      <Modal
        title={isEditMode ? "Editar unidade" : "Nova unidade"}
        description={
          step === 1
            ? "Etapa 1 de 2 - Identificação"
            : "Etapa 2 de 2 - Financeiro"
        }
        triggerButton={triggerButton}
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          if (!open) {
            setStep(1);
            form.reset({ plans: [] });
          }
        }}
      >
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-16"
            onSubmit={(e) => e.preventDefault()}
          >
            {step === 1 && <FirstStep isEditMode={isEditMode} />}
            {step === 2 && <SecondStep />}
            <div className="flex gap-4">
              {step !== 1 && (
                <Button
                  variant="outline"
                  className="w-full font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
                  onClick={() => setStep(step - 1)}
                >
                  Voltar
                </Button>
              )}
              {!isLastStep ? (
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleContinue}
                  disabled={
                    step === 1 &&
                    (!!form.formState.errors.cnpj ||
                      (form.watch("cnpj") || "").replace(/\D/g, "").length < 14)
                  }
                >
                  Continuar
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleConclude}
                  disabled={createMutation.isPending}
                >
                  {createMutation.isPending ? "Salvando..." : "Concluir"}
                </Button>
              )}
            </div>
          </form>
        </FormProvider>
      </Modal>

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={
          isEditMode
            ? "Unidade atualizada com sucesso!"
            : "Unidade cadastrada com sucesso!"
        }
      />
    </>
  );
}
