import { GetBatchesResponse } from "./types";

export const mockedBatchesPage1: GetBatchesResponse = {
  data: [
    {
      id: "0004",
      sequenceNumber: 4,
      status: "PENDING",
      progress: 0,
      totals: { total: 0, error: 0, success: 0 },
      generatedAt: undefined,
    },
    {
      id: "0003",
      sequenceNumber: 3,
      status: "PROCESSING",
      progress: 50,
      totals: { total: 20, error: 4, success: 16 },
      generatedAt: undefined,
    },
    {
      id: "0002",
      sequenceNumber: 2,
      status: "COMPLETED",
      progress: 100,
      totals: { total: 15, error: 2, success: 13 },
      generatedAt: new Date().toISOString(),
    },
    {
      id: "0001",
      sequenceNumber: 1,
      status: "COMPLETED",
      progress: 100,
      totals: { total: 8, error: 2, success: 6 },
      generatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(),
    },
  ],
  pagination: { page: 1, limit: 10, total: 4, totalPages: 1 },
};
