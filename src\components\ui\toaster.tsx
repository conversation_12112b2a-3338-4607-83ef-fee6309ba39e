"use client";

import { useToast } from "@/hooks/use-toast";
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast";
import { Circle<PERSON>lert, CircleCheck } from "lucide-react";

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        const isDestructive = props.variant === "destructive";
        const isSuccess = props.variant === "success";
        const sideColor = isDestructive
          ? "#FF3B30"
          : isSuccess
          ? "#34C759"
          : undefined;
        const { style, ...rest } = props;
        const rootStyle = isDestructive
          ? { backgroundColor: "#EB0C00", borderColor: "#EB0C00" }
          : undefined;

        return (
          <Toast
            key={id}
            {...rest}
            style={{
              ...(style as React.CSSProperties),
              ...(rootStyle as React.CSSProperties),
            }}
            className="p-0 w-[512px] rounded-lg"
          >
            <div className="flex items-stretch w-full">
              {(isDestructive || isSuccess) && (
                <div
                  className="w-20 rounded-l-lg flex items-center justify-center p-6"
                  style={{ backgroundColor: sideColor }}
                >
                  {isDestructive ? (
                    <CircleAlert className="w-8 h-8 text-white" />
                  ) : (
                    <CircleCheck className="w-8 h-8 text-white" />
                  )}
                </div>
              )}

              <div className="flex-1 p-6">
                <div className="grid gap-6">
                  {title && (
                    <ToastTitle className="text-white text-xl leading-snug font-semibold tracking-normal">
                      {title}
                    </ToastTitle>
                  )}
                  {description && (
                    <ToastDescription className="text-white text-base leading-snug font-normal tracking-normal whitespace-pre-wrap break-words">
                      {description}
                    </ToastDescription>
                  )}
                </div>
                {action}
              </div>
            </div>

            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport className="fixed right-6 top-6 z-[100] w-[512px] p-0" />
    </ToastProvider>
  );
}
