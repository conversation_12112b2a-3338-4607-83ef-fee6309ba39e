"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { ResetPasswordFormValues, resetPasswordSchema } from "./validations";
import { resetPassword } from "@/http/auth";

type ResetPasswordPageProps = {
  token: string;
};

export function ResetPasswordPage({ token }: ResetPasswordPageProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(false);

  const {
    formState: { errors },
    register,
    handleSubmit,
  } = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: ResetPasswordFormValues) => {
    try {
      setIsLoading(true);
      const response = await resetPassword({
        token,
        password: data.password,
        confirmPassword: data.confirmPassword,
      });

      const { status, message, errors } = (response?.data ?? {}) as {
        status?: string;
        message?: string;
        errors?: Array<{ path?: string; message: string }>;
      };

      if (status === "error") {
        throw { response: { data: { message, errors } } };
      }

      toast({
        title: "Senha redefinida",
        description: "Sua senha foi alterada com sucesso.",
        variant: "success",
      });
      router.replace("/");
    } catch (error: unknown) {
      const description =
        (
          error as {
            response?: {
              data?: { message?: string; errors?: Array<{ message: string }> };
            };
          }
        )?.response?.data?.message ||
        (
          error as {
            response?: {
              data?: { message?: string; errors?: Array<{ message: string }> };
            };
          }
        )?.response?.data?.errors?.[0]?.message ||
        "Não foi possível redefinir sua senha. Tente novamente.";
      toast({ title: "Erro", description, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="hidden lg:flex lg:w-1/2 bg-gray-50 items-center justify-center p-12">
        <div className="max-w-lg">
          <Image
            src="/saudeDaGenteLogo.png"
            alt="Logo"
            width={360}
            height={160}
          />
        </div>
      </div>

      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          <h2 className="text-3xl font-bold tracking-tight text-center">
            Redefinir senha
          </h2>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-6"
            autoComplete="off"
          >
            <Input
              label="Nova senha"
              type="password"
              placeholder="Digite a nova senha"
              autoComplete="new-password"
              errorMessage={errors.password?.message}
              {...register("password")}
            />
            <Input
              label="Confirmar nova senha"
              type="password"
              placeholder="Confirme a nova senha"
              autoComplete="new-password"
              errorMessage={errors.confirmPassword?.message}
              {...register("confirmPassword")}
            />

            <div className="space-y-4 pt-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Redefinindo...</span>
                  </div>
                ) : (
                  "Redefinir senha"
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
