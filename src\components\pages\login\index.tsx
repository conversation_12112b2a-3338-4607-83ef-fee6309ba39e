"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth-context";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { LoginFormValues, loginSchema } from "./validations";
import { useEffect } from "react";

export function LoginPage() {
  const { signIn, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  const {
    formState: { errors },
    register,
    handleSubmit,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.replace("/sindicatos");
    }
  }, [isAuthenticated, router]);

  const onSubmit = async (data: LoginFormValues) => {
    try {
      await signIn(data.email, data.password);
      router.replace("/sindicatos");
    } catch {
      // Error is handled in the context with toast
    }
  };
  return (
    <>
      <div className="hidden lg:flex lg:w-1/2 bg-gray-50 items-center justify-center p-12">
        <div className="max-w-lg">
          <Image
            src="/saudeDaGenteLogo.png"
            alt="Logo"
            width={360}
            height={160}
          />
        </div>
      </div>

      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          <h2 className="text-3xl font-bold tracking-tight text-center">
            Acessar minha conta
          </h2>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <Input
              label="E-mail"
              type="email"
              placeholder="Digite seu email"
              errorMessage={errors.email?.message}
              {...register("email")}
            />
            <Input
              label="Senha"
              type="password"
              errorMessage={errors.password?.message}
              placeholder="Digite sua senha"
              {...register("password")}
            />
            <div className="space-y-4 pt-8">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Entrando...</span>
                  </div>
                ) : (
                  "Entrar"
                )}
              </Button>
            </div>
          </form>

          <div className="text-center">
            <Link
              href="/esqueci-senha"
              className="text-sm text-primary font-bold hover:text-primary/80 transition-colors"
            >
              Esqueci minha senha
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
