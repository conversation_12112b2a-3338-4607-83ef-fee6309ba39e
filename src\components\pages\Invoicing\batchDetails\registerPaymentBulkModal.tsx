/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React from "react";
import { Modal } from "@/components/modals";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DialogClose } from "@/components/ui/dialog";

type RegisterPaymentBulkModalProps = {
  trigger: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSave?: (status: "Pendente" | "Pago" | "Vencido") => void;
};

export function RegisterPaymentBulkModal({
  trigger,
  open,
  onOpenChange,
  onSave,
}: RegisterPaymentBulkModalProps) {
  const [status, setStatus] = React.useState<"Pendente" | "Pago" | "Vencido">(
    "Pendente"
  );

  return (
    <Modal
      title="Atualizar status do pagamento"
      triggerButton={trigger}
      headerAlign="center"
      open={open}
      onOpenChange={(o) => {
        if (!o) setStatus("Pendente");
        onOpenChange?.(o);
      }}
    >
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-3">
          <span className="text-base text-[#333333]">Status do pagamento</span>
          <Select value={status} onValueChange={(v) => setStatus(v as any)}>
            <SelectTrigger className="h-12 text-base">
              <SelectValue placeholder="Selecione" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Pendente">Pendente</SelectItem>
              <SelectItem value="Pago">Pago</SelectItem>
              <SelectItem value="Vencido">Vencido</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center justify-between gap-4 pt-2">
          <DialogClose asChild>
            <Button
              variant="outline"
              className="flex-1 font-bold text-secondary border-secondary hover:text-secondary hover:border-secondary"
            >
              Cancelar
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              className="flex-1 font-bold"
              variant="secondary"
              onClick={() => onSave?.(status)}
            >
              Salvar alterações
            </Button>
          </DialogClose>
        </div>
      </div>
    </Modal>
  );
}
