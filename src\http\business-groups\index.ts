import { api } from "@/services/api";
import {
  ICreateBusinessGroup,
  IGetBusinessGroups,
  IGetBusinessGroupById,
  IUpdateBusinessGroup,
} from "./types";

export function createBusinessGroup({
  unionId,
  consultancyId,
  cnpj,
  alias,
  name,
  address,
  cnae,
  contact,
  financeContact,
}: ICreateBusinessGroup) {
  const response = api.post("/api/business-groups", {
    unionId,
    consultancyId,
    cnpj,
    alias,
    name,
    address,
    cnae,
    contact,
    financeContact,
  });

  return response;
}

export function getBusinessGroups({
  page,
  limit,
  search,
  unionIds,
  consultancyIds,
}: IGetBusinessGroups) {
  const params = new URLSearchParams();

  if (page !== undefined) params.append("page", page.toString());
  if (limit !== undefined) params.append("limit", limit.toString());
  if (search) params.append("search", search);
  if (unionIds?.length) params.append("unionIds", unionIds.join(","));
  if (consultancyIds?.length)
    params.append("consultancyIds", consultancyIds.join(","));

  const response = api.get(`/api/business-groups?${params.toString()}`);

  return response;
}

export function getBusinessGroupById({ id }: IGetBusinessGroupById) {
  const response = api.get(`/api/business-groups/${id}`);

  return response;
}

export function updateBusinessGroup({
  id,
  alias,
  contact,
  financeContact,
}: IUpdateBusinessGroup) {
  const response = api.put(`/api/business-groups/${id}`, {
    alias,
    contact,
    financeContact,
  });

  return response;
}
