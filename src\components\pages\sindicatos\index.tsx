"use client";

import { Plus, Users, Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import { SindicatoModalForm } from "./sindicatoModal";
import { SindicatosTable } from "@/components/tables/sindicatos";
import { useState } from "react";
import { Input } from "@/components/ui/input";

export function SindicatosPage() {
  const [search, setSearch] = useState("");
  return (
    <div className="w-full">
      <header className="w-full h-16 bg-[#256caf16] text-primary font-bold flex items-center justify-between p-6 gap-4">
        <div className="flex gap-1">
          <Users size={20} absoluteStrokeWidth />
          <span>Sindicatos</span>
        </div>
        <div className="flex items-center gap-4 ml-auto">
          <div className="relative w-80">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4 z-10 pointer-events-none" />
            <Input
              placeholder="CNPJ ou nome fantasia"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 font-normal text-black"
            />
          </div>
          <SindicatoModalForm
            triggerButton={
              <Button variant="secondary" className="h-10 px-8">
                <Plus size={20} />
                Novo Sindicato
              </Button>
            }
          />
        </div>
      </header>
      <div className="w-full">
        <SindicatosTable search={search} />
      </div>
    </div>
  );
}
