"use client";

import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { DataTable } from "../ui/datatable";
import { Button } from "../ui/button";
import { Edit2, ListFilter } from "lucide-react";
import { Column } from "@/types/table";
import { formatCNPJ } from "@/lib/format";
import { getBusinessUnits } from "@/http/business-units";
import type { IGetBusinessUnits } from "@/http/business-units/types";

type ApiBusinessUnit = {
  id?: string;
  cnpj?: string;
  alias?: string;
  name?: string;
  dueDay?: number;
  businessGroup?: { id?: string; alias?: string; name?: string };
  businessUnitPlans?: Array<{ employeeCount?: number }>;
};
import { UnityModalForm } from "@/components/pages/unityManagement/unityModal";
import { UnityFilterModal } from "@/components/pages/unityManagement/filter";

type BusinessUnitRow = {
  id: string;
  cnpj: string;
  alias: string;
  name?: string;
  groupAlias?: string;
  groupId?: string;
  dueDay?: number;
  employees?: number;
};

export function UnityManagementTable({ search = "" }: { search?: string }) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedGroupIds, setSelectedGroupIds] = useState<string[]>([]);
  const [dueDayStart, setDueDayStart] = useState<number | undefined>(undefined);
  const [dueDayEnd, setDueDayEnd] = useState<number | undefined>(undefined);
  const [employeeCountStart, setEmployeeCountStart] = useState<
    number | undefined
  >(undefined);
  const [employeeCountEnd, setEmployeeCountEnd] = useState<number | undefined>(
    undefined
  );
  const [filtersVersion, setFiltersVersion] = useState(0);
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery<{
    businessUnits: ApiBusinessUnit[];
    totalPages: number;
  }>({
    queryKey: [
      "business-units",
      currentPage,
      pageSize,
      search,
      selectedGroupIds,
      dueDayStart,
      dueDayEnd,
      employeeCountStart,
      employeeCountEnd,
      filtersVersion,
    ],
    queryFn: async () => {
      const res = await getBusinessUnits({
        page: currentPage,
        limit: pageSize,
        search: search || undefined,
        businessGroupIds: selectedGroupIds.length
          ? selectedGroupIds
          : undefined,
        dueDayStart,
        dueDayEnd,
        employeeCountStart,
        employeeCountEnd,
      } satisfies IGetBusinessUnits);
      const payload = (
        res as unknown as {
          data?: { businessUnits: ApiBusinessUnit[]; totalPages: number };
        }
      ).data;
      return (
        payload ??
        (res as unknown as {
          businessUnits: ApiBusinessUnit[];
          totalPages: number;
        })
      );
    },
  });

  const sourceList = (data?.businessUnits || []) as Array<ApiBusinessUnit>;
  const totalPages = data?.totalPages || 0;

  const rows: BusinessUnitRow[] = sourceList.map((u) => ({
    id: String(u.id || ""),
    cnpj: String(u.cnpj || ""),
    alias: String(u.alias || ""),
    name: String(u.name || ""),
    groupAlias: String(u.businessGroup?.alias || u.businessGroup?.name || ""),
    groupId: String(u.businessGroup?.id || ""),
    dueDay: Number(u.dueDay || 0),
    employees: Number(
      Array.isArray(u.businessUnitPlans)
        ? u.businessUnitPlans.reduce(
            (acc: number, p: { employeeCount?: number }) =>
              acc + (Number(p?.employeeCount || 0) || 0),
            0
          )
        : 0
    ),
  }));

  const columns: Column<BusinessUnitRow>[] = [
    { header: "CNPJ", render: (r) => formatCNPJ(r.cnpj) },
    { header: "Nome fantasia", render: (r) => r.alias },
    { header: "Grupo", render: (r) => r.groupAlias || "-" },
    {
      header: "Vencimento fatura",
      render: (r) => (r.dueDay ? String(r.dueDay) : "-"),
    },
    { header: "Funcionários", render: (r) => String(r.employees ?? 0) },
    {
      isActionColumn: true,
      header: "",
      render: (row) => (
        <UnityModalForm
          isEditMode
          id={row.id}
          prefill={{
            cnpj: row.cnpj,
            alias: row.alias,
            name: row.name,
            dueDay: row.dueDay,
            businessGroup: row.groupId
              ? { value: row.groupId, label: row.groupAlias || "" }
              : undefined,
          }}
          triggerButton={
            <Button
              className="w-7 h-7"
              aria-label={`Editar unidade ${row.alias}`}
            >
              <Edit2 size={16} className="w-4 h-4" />
            </Button>
          }
        />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={rows}
      keyExtractor={(item) => item.id}
      className="w-full p-8"
      isLoading={isLoading}
      openFilterModal={
        <UnityFilterModal
          defaultGroupIds={selectedGroupIds}
          onApply={({ groupIds, dueStart, dueEnd, empStart, empEnd }) => {
            setSelectedGroupIds(groupIds);
            setDueDayStart(dueStart);
            setDueDayEnd(dueEnd);
            setEmployeeCountStart(empStart);
            setEmployeeCountEnd(empEnd);
            setCurrentPage(1);
            setFiltersVersion((v) => v + 1);
            queryClient.invalidateQueries({ queryKey: ["business-units"] });
          }}
          triggerButton={
            <Button variant="ghost" className="px-2 h-8 border border-gray-200">
              <ListFilter />
            </Button>
          }
        />
      }
      pagination={{
        currentPage,
        totalPages,
        onPageChange: setCurrentPage,
        pageSize,
        onPageSizeChange: (newSize) => {
          setPageSize(newSize);
          setCurrentPage(1);
        },
      }}
    />
  );
}
