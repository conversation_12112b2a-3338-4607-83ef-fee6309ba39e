export type IGetUsers = {
  page?: number;
  limit?: number;
  search?: string;
  roles?: string[];
  unionIds?: string[];
  businessGroupIds?: string[];
  businessUnitIds?: string[];
  consultancyIds?: string[];
  isActive?: boolean;
};

export type IGetUserById = {
  id: string;
};

export type IUpdateUser = {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  role?:
    | "ADMIN_CLIENT"
    | "ADMIN_SDG"
    | "ATTENDANT_SDG"
    | "ACCOUNTANT"
    | "CONTROLLER_SDG"
    | "FINANCIAL_CLIENT"
    | "FINANCIAL_SDG";
  isActive?: boolean;
  unionIds?: string[];
  businessGroupIds?: string[];
  businessUnitIds?: string[];
  consultancyIds?: string[];
};

// API contract (subset used on list/table)
export type UserListItem = {
  id: string;
  email: string;
  name: string;
  phone: string;
  role:
    | "ADMIN_CLIENT"
    | "ADMIN_SDG"
    | "ATTENDANT_SDG"
    | "ACCOUNTANT"
    | "CONTROLLER_SDG"
    | "FINANCIAL_CLIENT"
    | "FINANCIAL_SDG";
  isActive: boolean;
  unions?: Array<{ id: string; name: string }>;
  businessGroups?: Array<{ id: string; name: string }>;
  businessUnits?: Array<{ id: string; name: string }>;
  consultancies?: Array<{ id: string; name: string }>;
};

export type GetUsersResponse = {
  users: UserListItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type GetUserByIdResponse = {
  user: UserListItem; // simplify for now
};

export type UpdateUserResponse = {
  id: string;
};

export type ResetUserPasswordParams = {
  id: string;
};
