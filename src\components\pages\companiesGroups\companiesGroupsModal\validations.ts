import { INVALID_EMAIL_MESSAGE, REQUIRED_MESSAGE } from "@/validations";
import { z } from "zod";

export const companiesGroupsValidation = z.object({
  cnpj: z.string().nonempty({ message: REQUIRED_MESSAGE }),
  union: z
    .any({ required_error: REQUIRED_MESSAGE })
    .refine((val) => <PERSON><PERSON><PERSON>(val), { message: REQUIRED_MESSAGE }),
  consultancy: z.any().optional(),
  fantasyName: z.string().nonempty({ message: REQUIRED_MESSAGE }),
  socialReason: z.string().nonempty({ message: REQUIRED_MESSAGE }),
  address: z.string().nonempty({ message: REQUIRED_MESSAGE }),
  addressObj: z
    .object({
      street: z.string().min(1).optional(),
      number: z.string().min(1).optional(),
      complement: z.string().optional(),
      zip: z.string().min(1).optional(),
      neighborhood: z.string().min(1).optional(),
      city: z.string().min(1).optional(),
      state: z.string().min(1).optional(),
    })
    .optional(),
  contact: z.object({
    name: z.string().min(1, { message: REQUIRED_MESSAGE }),
    phone: z.string().min(1, { message: REQUIRED_MESSAGE }),
    email: z.string().email(INVALID_EMAIL_MESSAGE),
  }),
  finance: z
    .object({
      name: z.string().min(1, { message: REQUIRED_MESSAGE }).optional(),
      phone: z.string().min(1, { message: REQUIRED_MESSAGE }).optional(),
      email: z.string().email(INVALID_EMAIL_MESSAGE).optional(),
    })
    .optional(),
});

export type companiesGroupsValidationType = z.infer<
  typeof companiesGroupsValidation
>;
