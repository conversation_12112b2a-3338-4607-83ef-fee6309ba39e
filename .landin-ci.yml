stages:
  - build
  - deploy

variables:
  TAG_LATEST: $CI_REGISTRY_IMAGE:latest
  TAG_COMMIT: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  TAG_COMMIT_FULL: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  NEXT_PUBLIC_API_BASE_URL: "https://saudedagente-sindicatobackend-development.landincloud.app"


build:
  stage: build
  #when: manual
  image: docker:25
  services:
    - name: docker:25-dind
  before_script:
    - apk add --no-cache git
    - echo "$CI_REGISTRY_PASSWORD" | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin
  script:
    - docker build -t $TAG_COMMIT -t $TAG_LATEST --build-arg NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL .
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
    - docker image ls

deploy_prod:
  stage: deploy
  image: ubuntu:latest
  environment:
    name: production
  only: 
    - main
  variables:
    SSH_HOST: $SSH_HOST64
    SSH_PK: $SSH_PK_PROD
  before_script:
    - bash $PREPARE_SSH
    - touch .env && cat $ENV > .env
  script:
    - scp docker-compose.yml .env $SSH_USER@$SSH_HOST:$PROJECT_PATH
    - ssh $SSH_USER@$SSH_HOST  "cd $PROJECT_PATH && docker compose pull && docker compose up -d && docker compose ps && exit"
    - sleep 10
    - ssh $SSH_USER@$SSH_HOST  "cd $PROJECT_PATH && docker compose logs && exit"


deploy_dev:
  stage: deploy
  image: ubuntu:latest
  environment:
    name: development
  only: 
    - dev
  variables:
    SSH_HOST: $SSH_HOST55_DEV
    SSH_PK: $SSH_PK_DEV
  before_script:
    - touch .env && cat $ENV > .env
    - bash $PREPARE_SSH
  script:
    - scp docker-compose.yml .env $SSH_USER@$SSH_HOST:$PROJECT_PATH
    - ssh $SSH_USER@$SSH_HOST  "cd $PROJECT_PATH && docker compose pull && docker compose up -d && docker compose ps && exit"
    - sleep 10
    - ssh $SSH_USER@$SSH_HOST  "cd $PROJECT_PATH && docker compose logs && exit"