export type InvoicingBatchStatus =
  | "PENDING"
  | "PROCESSING"
  | "COMPLETED"
  | "FAILED";

export type InvoicingBatch = {
  id: string;
  sequenceNumber: number;
  status: InvoicingBatchStatus;
  progress: number; // 0 - 100
  totals: {
    total: number;
    error: number;
    success: number;
  };
  generatedAt?: string; // ISO date
};

export type GetBatchesResponse = {
  data: InvoicingBatch[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};
