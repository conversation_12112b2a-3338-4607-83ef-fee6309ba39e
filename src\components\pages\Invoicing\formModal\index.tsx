/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React from "react";
import { Modal } from "@/components/modals";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import {
  invoicingBatchValidation,
  type InvoicingBatchValidationType,
} from "./validations";
import { SearchableMultiSelect } from "@/components/ui/multiselect";
import { SuccessModal } from "@/components/modals/success-modal";
import { useRouter } from "next/navigation";
import type { LabelAndValue } from "@/types/labelAndValue";
import { useToast } from "@/hooks/use-toast";
import type { Union } from "@/http/unions/types";
import type { CreateBillingBatchRequest } from "@/http/billing/types";

type UserModalFormProps = {
  triggerButton: React.ReactNode;
};

export function InvoicingModalForm({ triggerButton }: UserModalFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { handleSubmit, control, setValue, watch, formState } =
    useForm<InvoicingBatchValidationType>({
      resolver: zodResolver(invoicingBatchValidation),
      defaultValues: {
        selectedUnionIds: [],
        selectedBusinessGroupIds: [],
        selectedBusinessUnitIds: [],
        baseDueDayStart: 1,
        baseDueDayEnd: 31,
        invoiceDueDate: "",
        maintainDueDate: true,
      },
    });

  const { errors } = formState;

  const selectedUnionIds = watch("selectedUnionIds");
  const selectedBusinessGroupIds = watch("selectedBusinessGroupIds");
  const maintainDueDate = watch("maintainDueDate");

  const [unions, setUnions] = React.useState<LabelAndValue[]>([]);
  const [businessGroups, setBusinessGroups] = React.useState<LabelAndValue[]>(
    []
  );
  const [businessUnits, setBusinessUnits] = React.useState<LabelAndValue[]>([]);

  // quando optar por manter o vencimento do cadastro, limpar o campo de data
  React.useEffect(() => {
    if (maintainDueDate) {
      setValue("invoiceDueDate", "");
    }
  }, [maintainDueDate, setValue]);

  React.useEffect(() => {
    // carregar sindicatos ao abrir
    (async () => {
      let active = true;
      try {
        const { getUnions } = await import("@/http/unions");
        const { withRetry } = await import("@/lib/utils");
        const res = await withRetry(() => getUnions({ page: 1, limit: 50 }), {
          retries: 2,
          baseDelayMs: 400,
        });
        const opts = (res.unions || []).map((u: Union) => ({
          label: u.alias || u.name,
          value: u.id,
        }));
        if (!active) return;
        setUnions(opts);
      } catch (error) {
        if (!active) return;
        setUnions([]);
        try {
          const { extractApiErrorMessage } = await import("@/lib/utils");
          toast({
            title: "Erro",
            description: extractApiErrorMessage(
              error,
              "Erro ao carregar sindicatos"
            ),
            variant: "destructive",
          });
        } catch {}
      }
      return () => {
        active = false;
      };
    })();
  }, []);

  React.useEffect(() => {
    // quando mudar sindicatos, limpar e carregar grupos
    (async () => {
      let active = true;
      try {
        setBusinessGroups([]);
        setValue("selectedBusinessGroupIds", []);
        setBusinessUnits([]);
        setValue("selectedBusinessUnitIds", []);
        if (!selectedUnionIds?.length) return;
        const { getBusinessGroups } = await import("@/http/business-groups");
        const { withRetry } = await import("@/lib/utils");
        const res = await withRetry(() =>
          getBusinessGroups({ unionIds: selectedUnionIds })
        );
        const list = (await res).data?.businessGroups as
          | Array<{ id: string; alias?: string; name: string }>
          | undefined;
        const opts = (list || []).map((g) => ({
          label: g.alias || g.name,
          value: g.id,
        }));
        if (!active) return;
        setBusinessGroups(opts);
      } catch (error) {
        if (!active) return;
        setBusinessGroups([]);
        try {
          const { extractApiErrorMessage } = await import("@/lib/utils");
          toast({
            title: "Erro",
            description: extractApiErrorMessage(
              error,
              "Erro ao carregar grupos empresariais"
            ),
            variant: "destructive",
          });
        } catch {}
      }
      return () => {
        active = false;
      };
    })();
  }, [JSON.stringify(selectedUnionIds)]);

  React.useEffect(() => {
    // quando mudar grupos, limpar e carregar unidades
    (async () => {
      let active = true;
      try {
        setBusinessUnits([]);
        setValue("selectedBusinessUnitIds", []);
        if (!selectedBusinessGroupIds?.length) return;
        const { getBusinessUnits } = await import("@/http/business-units");
        const { withRetry } = await import("@/lib/utils");
        const res = await withRetry(() =>
          getBusinessUnits({ businessGroupIds: selectedBusinessGroupIds })
        );
        const list = (await res).data?.businessUnits as
          | Array<{ id: string; alias?: string; name: string }>
          | undefined;
        const opts = (list || []).map((u) => ({
          label: u.alias || u.name,
          value: u.id,
        }));
        if (!active) return;
        setBusinessUnits(opts);
      } catch (error) {
        if (!active) return;
        setBusinessUnits([]);
        try {
          const { extractApiErrorMessage } = await import("@/lib/utils");
          toast({
            title: "Erro",
            description: extractApiErrorMessage(
              error,
              "Erro ao carregar unidades"
            ),
            variant: "destructive",
          });
        } catch {}
      }
      return () => {
        active = false;
      };
    })();
  }, [JSON.stringify(selectedBusinessGroupIds)]);

  const handleSubmitInvoicing = async (
    values: InvoicingBatchValidationType
  ) => {
    try {
      const { createBillingBatch } = await import("@/http/billing");
      const payload: CreateBillingBatchRequest = {
        ...values,
        invoiceDueDate: values.invoiceDueDate ?? "",
      };
      await createBillingBatch(payload);
      setIsFormOpen(false);
      setSuccessOpen(true);
    } catch (error: unknown) {
      const { extractApiErrorMessage } = await import("@/lib/utils");
      toast({
        title: "Erro",
        description: extractApiErrorMessage(error, "Erro ao gerar faturamento"),
        variant: "destructive",
      });
    }
  };

  const [successOpen, setSuccessOpen] = React.useState(false);
  const [isFormOpen, setIsFormOpen] = React.useState(false);

  return (
    <>
      <Modal
        title="Novo Faturamento"
        triggerButton={triggerButton}
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
      >
        <form
          className="flex flex-col gap-4"
          onSubmit={handleSubmit(handleSubmitInvoicing)}
        >
          <div className="flex flex-col gap-6">
            <Controller
              name="selectedUnionIds"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Sindicatos"
                  placeholder="Selecione os sindicatos"
                  options={unions}
                  value={unions.filter((o) => field.value?.includes(o.value))}
                  onChange={(opts) => field.onChange(opts.map((o) => o.value))}
                  errorMessage={errors.selectedUnionIds?.message as string}
                />
              )}
            />
            <Controller
              name="selectedBusinessGroupIds"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Grupos empresariais"
                  placeholder={
                    selectedUnionIds?.length
                      ? "Selecione os grupos empresariais"
                      : "Selecione primeiro os sindicatos"
                  }
                  options={businessGroups}
                  value={businessGroups.filter((o) =>
                    field.value?.includes(o.value)
                  )}
                  onChange={(opts) => field.onChange(opts.map((o) => o.value))}
                  disabled={!selectedUnionIds?.length}
                  errorMessage={
                    errors.selectedBusinessGroupIds?.message as string
                  }
                />
              )}
            />
            <Controller
              name="selectedBusinessUnitIds"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Unidades"
                  placeholder={
                    selectedBusinessGroupIds?.length
                      ? "Selecione as unidades"
                      : "Selecione primeiro os grupos"
                  }
                  options={businessUnits}
                  value={businessUnits.filter((o) =>
                    field.value?.includes(o.value)
                  )}
                  onChange={(opts) => field.onChange(opts.map((o) => o.value))}
                  disabled={!selectedBusinessGroupIds?.length}
                  errorMessage={
                    errors.selectedBusinessUnitIds?.message as string
                  }
                />
              )}
            />
          </div>

          <span className="bg-primary-foreground/10 text-primary text-sm font-bold rounded-sm px-3 py-1.5 mt-2">
            Dia do vencimento base
          </span>

          <div className="flex flex-col gap-6 w-full mb-8">
            <div className="flex gap-4 w-full">
              <Controller
                name="baseDueDayStart"
                control={control}
                render={({ field }) => (
                  <Input
                    type="number"
                    min={1}
                    max={31}
                    label="Dia inicial"
                    placeholder="Ex.: 5"
                    value={field.value}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                    errorMessage={errors.baseDueDayStart?.message as string}
                  />
                )}
              />
              <Controller
                name="baseDueDayEnd"
                control={control}
                render={({ field }) => (
                  <Input
                    type="number"
                    min={1}
                    max={31}
                    label="Dia final"
                    placeholder="Ex.: 10"
                    value={field.value}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                    errorMessage={errors.baseDueDayEnd?.message as string}
                  />
                )}
              />
            </div>
            <Controller
              name="invoiceDueDate"
              control={control}
              render={({ field }) => (
                <Input
                  type="date"
                  label="Data de vencimento das faturas"
                  placeholder="yyyy-mm-dd"
                  value={field.value}
                  onChange={field.onChange}
                  disabled={maintainDueDate}
                  errorMessage={errors.invoiceDueDate?.message as string}
                />
              )}
            />

            <div className="flex items-center gap-2">
              <Controller
                name="maintainDueDate"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    className="h-5 w-5"
                    checked={field.value}
                    onCheckedChange={(v) => field.onChange(Boolean(v))}
                  />
                )}
              />
              <Label className="text-sm text-gray-700 cursor-pointer">
                Manter o vencimento do cadastro do cliente
              </Label>
            </div>
          </div>

          <Button type="submit" variant="secondary">
            Gerar faturas
          </Button>
        </form>
      </Modal>
      <SuccessModal
        isOpen={successOpen}
        onClose={() => {
          setSuccessOpen(false);
          setValue("selectedUnionIds", []);
          setValue("selectedBusinessGroupIds", []);
          setValue("selectedBusinessUnitIds", []);
          setValue("baseDueDayStart", 1);
          setValue("baseDueDayEnd", 31);
          setValue("invoiceDueDate", "");
          setValue("maintainDueDate", true);
          router.push("/faturamento");
          try {
            router.refresh();
            window.dispatchEvent(new Event("refresh-batches"));
          } catch {}
        }}
        title="As faturas estão sendo geradas"
        message={"Avisaremos você por e-mail quando\no processo for concluído!"}
      />
    </>
  );
}
