"use client";

import {
  More<PERSON><PERSON><PERSON><PERSON>,
  Eye,
  Pencil,
  CheckCircle2,
  Download,
  Barcode,
} from "lucide-react";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { InvoicingBatch } from "./types";
import { useBatchProgressSSE } from "@/hooks/use-batch-progress-sse";
import { useIsInViewport } from "@/hooks/use-is-in-viewport";

type BatchCardProps = {
  batch: InvoicingBatch;
};

function renderStatusLabel(status: InvoicingBatch["status"]) {
  if (status === "PENDING")
    return (
      <span className="text-xs bg-gray-100 text-gray-600 rounded-sm px-2 py-1">
        Pendente
      </span>
    );
  if (status === "PROCESSING")
    return (
      <span className="text-xs bg-blue-100 text-blue-700 rounded-sm px-2 py-1">
        Em processamento
      </span>
    );
  if (status === "COMPLETED")
    return (
      <span className="text-xs bg-emerald-100 text-emerald-700 rounded-sm px-2 py-1">
        Concluído
      </span>
    );
  return (
    <span className="text-xs bg-red-100 text-red-700 rounded-sm px-2 py-1">
      Falhou
    </span>
  );
}

export function BatchCard({ batch }: BatchCardProps) {
  const router = useRouter();
  const { ref, isInViewport } = useIsInViewport<HTMLDivElement>({
    root: null,
    rootMargin: "100px",
    threshold: 0.1,
  });
  const shouldTrack =
    (batch.status === "PROCESSING" || batch.status === "PENDING") &&
    isInViewport;
  const {
    progress: liveProgress,
    status: liveStatus,
    processedInvoices,
    totalExpectedInvoices,
    failedInvoices,
  } = useBatchProgressSSE(batch.id, shouldTrack);

  const effectiveProgress = Math.min(
    100,
    Math.max(0, Number(liveProgress ?? batch.progress ?? 0))
  );
  const effectiveStatus = (liveStatus ||
    batch.status) as InvoicingBatch["status"];
  const percentageFormatted = `${effectiveProgress}%`;

  const generatedAtText = batch.generatedAt
    ? new Date(batch.generatedAt).toLocaleString("pt-BR")
    : "—";

  return (
    <div
      ref={ref}
      role="button"
      tabIndex={0}
      onClick={() => router.push(`/faturamento/lotes/${batch.id}`)}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ")
          router.push(`/faturamento/lotes/${batch.id}`);
      }}
      className="cursor-pointer text-left w-full bg-white rounded-md border p-6 shadow-sm hover:shadow transition-shadow"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-2">
          <span className="font-semibold text-gray-900">
            Lote {String(batch.sequenceNumber).padStart(4, "0")}
          </span>
          {renderStatusLabel(effectiveStatus)}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              aria-label="Ações do lote"
              onClick={(e) => e.stopPropagation()}
              className="w-9 h-9 rounded-md flex items-center justify-center text-gray-500 hover:bg-primary hover:text-white"
            >
              <MoreHorizontal size={18} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-72">
            <DropdownMenuItem
              onClick={() => router.push(`/faturamento/lotes/${batch.id}`)}
            >
              <Eye className="mr-2 h-4 w-4 text-primary" /> Visualizar
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Pencil className="mr-2 h-4 w-4 text-primary" /> Editar vencimento
            </DropdownMenuItem>
            <DropdownMenuItem>
              <CheckCircle2 className="mr-2 h-4 w-4 text-primary" /> Atualizar
              status do pagamento
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Download className="mr-2 h-4 w-4 text-primary" /> Exportar
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Barcode className="mr-2 h-4 w-4 text-primary" /> Enviar 2ª via
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="mt-4">
        <span className="text-sm text-gray-700">Progresso</span>
        <div className="mt-2 h-2 w-full bg-gray-100 rounded-sm overflow-hidden">
          <div
            className="h-2 bg-emerald-500"
            style={{ width: `${effectiveProgress}%` }}
          />
        </div>
        <div className="text-xs text-gray-500 text-right mt-1">
          {percentageFormatted}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 mt-6">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Total</span>
          <span className="text-sm bg-gray-100 text-gray-700 rounded-sm px-2 py-1">
            {typeof totalExpectedInvoices === "number"
              ? totalExpectedInvoices
              : batch.totals.total || 0}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Erro</span>
          <span className="text-sm bg-red-100 text-red-700 rounded-sm px-2 py-1">
            {typeof failedInvoices === "number"
              ? failedInvoices
              : batch.totals.error || 0}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Sucesso</span>
          <span className="text-sm bg-emerald-100 text-emerald-700 rounded-sm px-2 py-1">
            {typeof processedInvoices === "number" &&
            typeof failedInvoices === "number"
              ? Math.max(0, processedInvoices - failedInvoices)
              : batch.totals.success || 0}
          </span>
        </div>
      </div>

      <div className="mt-6 pt-4 border-t text-xs text-gray-500">
        Gerado em: {generatedAtText}
      </div>
    </div>
  );
}
