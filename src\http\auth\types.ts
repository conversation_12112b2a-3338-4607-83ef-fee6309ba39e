export type ISignIn = {
  email: string;
  password: string;
};

export type IRegister = {
  email: string;
  name: string;
  phone: string;
  role:
    | "ADMIN_CLIENT"
    | "ADMIN_SDG"
    | "ATTENDANT_SDG"
    | "ACCOUNTANT"
    | "CONTROLLER_SDG"
    | "FINANCIAL_CLIENT"
    | "FINANCIAL_SDG";
  unionIds?: string[];
  businessGroupIds?: string[];
  businessUnitIds?: string[];
  consultancyIds?: string[];
};

export type IForgotPassword = {
  email: string;
};

export type IResetPassword = {
  token: string;
  password: string;
  confirmPassword: string;
};

export type IRefreshToken = {
  refreshToken: string;
};
