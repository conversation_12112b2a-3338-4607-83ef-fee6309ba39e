/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { Modal } from "@/components/modals";
import { SuccessModal } from "@/components/modals/success-modal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SearchableMultiSelect } from "@/components/ui/multiselect";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select } from "@/components/ui/selectUi";

import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import {
  UserValidation,
  UserValidationType,
  UserEditUiValidation,
} from "./validations";
import { maskPhoneBR, translateUserRole } from "@/lib/format";
import { updateUser, getUserById } from "@/http/users";
import { register as registerUser } from "@/http/auth";
import type { IRegister } from "@/http/auth/types";
import type { UserListItem } from "@/http/users/types";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getConsultancies } from "@/http/consultancies";
import { getUnions } from "@/http/unions";
import { getBusinessGroups } from "@/http/business-groups";
import { getBusinessUnits } from "@/http/business-units";
import React, { useState } from "react";

type OptionType = {
  value: string;
  label: string;
};

type UserModalFormProps = {
  isEditMode?: boolean;
  triggerButton: React.ReactNode;
  user?: {
    id: string;
    name: string;
    email: string;
    phone: string;
    role:
      | "ADMIN_CLIENT"
      | "ADMIN_SDG"
      | "ATTENDANT_SDG"
      | "ACCOUNTANT"
      | "CONTROLLER_SDG"
      | "FINANCIAL_CLIENT"
      | "FINANCIAL_SDG";
    isActive: boolean;
    unions?: Array<{ id: string; name: string }>;
    businessGroups?: Array<{ id: string; name: string }>;
    businessUnits?: Array<{ id: string; name: string }>;
    consultancies?: Array<{ id: string; name: string }>;
  };
};

const roleOptions = [
  { label: translateUserRole("ADMIN_CLIENT"), value: "ADMIN_CLIENT" },
  { label: translateUserRole("ADMIN_SDG"), value: "ADMIN_SDG" },
  { label: translateUserRole("ATTENDANT_SDG"), value: "ATTENDANT_SDG" },
  { label: translateUserRole("ACCOUNTANT"), value: "ACCOUNTANT" },
  { label: translateUserRole("CONTROLLER_SDG"), value: "CONTROLLER_SDG" },
  { label: translateUserRole("FINANCIAL_CLIENT"), value: "FINANCIAL_CLIENT" },
  { label: translateUserRole("FINANCIAL_SDG"), value: "FINANCIAL_SDG" },
];

export function UserModalForm({
  triggerButton,
  isEditMode,
  user,
}: UserModalFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [statusValue, setStatusValue] = useState<"active" | "inactive">(
    user?.isActive ? "active" : "inactive"
  );
  const [showSuccess, setShowSuccess] = useState(false);
  const [successTitle, setSuccessTitle] = useState("");
  const [open, setOpen] = useState(false);
  const [isHydrating, setIsHydrating] = useState(false);
  const [selectedUnions, setSelectedUnions] = useState<OptionType[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<OptionType[]>([]);
  const {
    control,
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    getValues,
    reset,
    watch,
    setValue,
  } = useForm<UserValidationType>({
    resolver: zodResolver(isEditMode ? UserEditUiValidation : UserValidation),
    defaultValues:
      isEditMode && user
        ? {
            name: user.name,
            email: user.email,
            phoneNumber: maskPhoneBR(user.phone || ""),
            profile: { label: translateUserRole(user.role), value: user.role },
            accountants: (user.consultancies || []).map((c) => ({
              label: c.name,
              value: c.id,
            })),
            unions: (user.unions || []).map((u) => ({
              label: u.name,
              value: u.id,
            })),
            groups: (user.businessGroups || []).map((g) => ({
              label: g.name,
              value: g.id,
            })),
            units: (user.businessUnits || []).map((u) => ({
              label: u.name,
              value: u.id,
            })),
          }
        : {
            name: "",
            email: "",
            phoneNumber: "",
            accountants: [],
            unions: [],
            groups: [],
            units: [],
          },
  });

  const watchedUnions = watch("unions");
  const watchedGroups = watch("groups");

  React.useEffect(() => {
    setSelectedUnions(watchedUnions || []);
  }, [watchedUnions]);

  React.useEffect(() => {
    setSelectedGroups(watchedGroups || []);
  }, [watchedGroups]);

  // Fetch options lists
  const { data: consultanciesData } = useQuery({
    queryKey: ["consultancies", { page: 1 }],
    queryFn: async () => {
      const res = await getConsultancies({
        page: 1,
        limit: 1000,
        isActive: true,
      });
      return res.data;
    },
  });
  type IdName = { id: string; name?: string; alias?: string };
  const consultancyOptions = (
    (consultanciesData?.consultancies || []) as IdName[]
  ).map((c) => ({ label: c.name || "", value: c.id }));

  const { data: unionsData } = useQuery({
    queryKey: ["unions", { page: 1 }],
    queryFn: () => getUnions({ page: 1, limit: 1000 }),
  });
  const unionOptions = ((unionsData?.unions || []) as IdName[]).map((u) => ({
    label: u.alias || u.name || "",
    value: u.id,
  }));

  const { data: groupsResp } = useQuery({
    queryKey: [
      "business-groups",
      { page: 1, unionIds: selectedUnions.map((u: OptionType) => u.value) },
    ],
    queryFn: async () => {
      if (selectedUnions.length === 0) return { businessGroups: [] };
      const res = await getBusinessGroups({
        page: 1,
        limit: 1000,
        unionIds: selectedUnions.map((u: OptionType) => u.value),
      });
      return (
        (res as unknown as { data?: { businessGroups?: IdName[] } }).data ||
        (res as unknown as { businessGroups?: IdName[] })
      );
    },
    enabled: selectedUnions.length > 0,
  });
  const groupOptions = ((groupsResp?.businessGroups || []) as IdName[]).map(
    (g) => ({
      label: g.alias || g.name || "",
      value: g.id,
    })
  );

  const { data: unitsResp } = useQuery({
    queryKey: [
      "business-units",
      { page: 1, businessGroupIds: selectedGroups.map((g: OptionType) => g.value) },
    ],
    queryFn: async () => {
      if (selectedGroups.length === 0) return { businessUnits: [] };
      const res = await getBusinessUnits({
        page: 1,
        limit: 1000,
        businessGroupIds: selectedGroups.map((g: OptionType) => g.value),
      });
      return (
        (res as unknown as { data?: { businessUnits?: IdName[] } }).data ||
        (res as unknown as { businessUnits?: IdName[] })
      );
    },
    enabled: selectedGroups.length > 0,
  });
  const unitOptions = ((unitsResp?.businessUnits || []) as IdName[]).map(
    (u) => ({
      label: u.alias || u.name || "",
      value: u.id,
    })
  );

  const handleSubmitUser = async (data: UserValidationType) => {
    try {
      if (isEditMode && user?.id) {
        const payload = {
          id: user.id,
          name: data.name,
          email: data.email,
          phone: (data.phoneNumber || "").replace(/\D/g, ""),
          role: data.profile?.value as IRegister["role"],
          isActive: statusValue === "active",
          unionIds: Array.isArray(data.unions)
            ? data.unions.map((o) => o.value)
            : [],
          businessGroupIds: Array.isArray(data.groups)
            ? data.groups.map((o) => o.value)
            : [],
          businessUnitIds: Array.isArray(data.units)
            ? data.units.map((o) => o.value)
            : [],
          consultancyIds: Array.isArray(data.accountants)
            ? data.accountants.map((o) => o.value)
            : [],
        };
        await updateUser(payload);
        setOpen(false);
        setSuccessTitle("Usuário atualizado com sucesso!");
        setShowSuccess(true);
        queryClient.invalidateQueries({ queryKey: ["users"] });
      } else {
        const payload = {
          email: (data.email || "").trim(),
          name: data.name,
          phone: (data.phoneNumber || "").replace(/\D/g, ""),
          role: data.profile?.value as IRegister["role"],
          unionIds: Array.isArray(data.unions)
            ? data.unions.map((o) => o.value)
            : [],
          businessGroupIds: Array.isArray(data.groups)
            ? data.groups.map((o) => o.value)
            : [],
          businessUnitIds: Array.isArray(data.units)
            ? data.units.map((o) => o.value)
            : [],
          consultancyIds: Array.isArray(data.accountants)
            ? data.accountants.map((o) => o.value)
            : [],
        };
        await registerUser(payload);
        setOpen(false);
        setSuccessTitle("Usuário cadastrado com sucesso!");
        setShowSuccess(true);
        queryClient.invalidateQueries({ queryKey: ["users"] });
      }
      reset();
    } catch (error: unknown) {
      console.error("[UserModal] submit error", error);
      const { extractApiErrorMessage } = await import("@/lib/utils");
      toast({
        title: "Erro",
        description: extractApiErrorMessage(error, "Erro ao salvar usuário"),
        variant: "destructive",
      });
    }
  };

  const handleInvalid = (errs: unknown) => {
    console.warn("[UserModal] validation errors", errs, getValues());
  };

  // Hydrate form with full details when opening in edit mode
  React.useEffect(() => {
    let cancelled = false;
    const hydrate = async () => {
      try {
        if (!open || !isEditMode || !user?.id) return;
        setIsHydrating(true);
        const res = await getUserById({ id: user.id });
        const detailed = ((res as unknown as { user?: unknown })?.user ??
          (res as unknown as { data?: { user?: unknown } })?.data?.user ??
          res) as Partial<UserListItem>;
        if (cancelled || !detailed) return;
        reset({
          name: detailed.name ?? user.name,
          email: detailed.email ?? user.email,
          phoneNumber: maskPhoneBR(detailed.phone ?? user.phone ?? ""),
          profile: {
            label: translateUserRole(detailed.role ?? user.role),
            value: detailed.role ?? user.role,
          },
          accountants: (
            (detailed.consultancies as Array<{ id: string; name: string }>) ??
            []
          ).map((c) => ({ label: c.name, value: c.id })),
          unions: (
            (detailed.unions as Array<{ id: string; name: string }>) ?? []
          ).map((u) => ({ label: u.name, value: u.id })),
          groups: (
            (detailed.businessGroups as Array<{ id: string; name: string }>) ??
            []
          ).map((g) => ({ label: g.name, value: g.id })),
          units: (
            (detailed.businessUnits as Array<{ id: string; name: string }>) ??
            []
          ).map((u) => ({ label: u.name, value: u.id })),
        });
        setStatusValue(
          detailed.isActive ?? user.isActive ? "active" : "inactive"
        );
      } catch (e) {
        console.error("[UserModal] hydrate error", e);
      } finally {
        setIsHydrating(false);
      }
    };
    void hydrate();
    return () => {
      cancelled = true;
    };
  }, [open, isEditMode, user?.id]);

  return (
    <>
      <Modal
        title={isEditMode ? "Editar usuário" : "Novo usuário"}
        triggerButton={triggerButton}
        open={open}
        onOpenChange={(newOpen) => setOpen(newOpen)}
      >
        <form
          className="flex flex-col gap-16"
          onSubmit={handleSubmit(handleSubmitUser, handleInvalid)}
        >
          <div className="space-y-6">
            <Input
              label="Nome"
              placeholder="Digite o nome."
              {...register("name")}
              errorMessage={errors.name?.message}
            />
            <Input
              label="Email"
              placeholder="Digite o e-maill"
              disabled={!!isEditMode}
              {...register("email")}
              errorMessage={errors.email?.message}
            />
            {(() => {
              const phoneReg = register("phoneNumber");
              return (
                <Input
                  label="Telefone"
                  placeholder="Digite o celular"
                  name={phoneReg.name}
                  ref={phoneReg.ref}
                  onBlur={phoneReg.onBlur}
                  onChange={(e) => {
                    e.target.value = maskPhoneBR(e.target.value);
                    phoneReg.onChange(e);
                  }}
                  onInput={(e) => {
                    const el = e.target as HTMLInputElement;
                    el.value = maskPhoneBR(el.value);
                    phoneReg.onChange(
                      e as unknown as React.ChangeEvent<HTMLInputElement>
                    );
                  }}
                  errorMessage={errors.phoneNumber?.message}
                />
              );
            })()}
            <Controller
              name="profile"
              control={control}
              render={({ field }) => (
                <Select
                  label="Perfil"
                  value={field.value}
                  onChange={field.onChange}
                  errorMessage={errors.profile?.message}
                  options={roleOptions}
                  placeholder="Selecione o perfil"
                />
              )}
            />
            <Controller
              name="accountants"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Contabilidades"
                  value={field.value ?? []}
                  onChange={field.onChange}
                  errorMessage={errors.accountants?.message}
                  options={consultancyOptions}
                  placeholder="Selecione as contabilidades permitidas."
                />
              )}
            />

            <Controller
              name="unions"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Sindicatos"
                  value={field.value ?? []}
                  onChange={(newValue) => {
                    field.onChange(newValue);
                    setValue("groups", []);
                    setValue("units", []);
                    setSelectedGroups([]);
                  }}
                  errorMessage={errors.unions?.message}
                  options={unionOptions}
                  placeholder="Selecione os sindicatos permitidos."
                />
              )}
            />

            <Controller
              name="groups"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Grupos"
                  value={field.value ?? []}
                  onChange={(newValue) => {
                    field.onChange(newValue);
                    setValue("units", []);
                  }}
                  errorMessage={errors.groups?.message}
                  options={groupOptions}
                  placeholder="Selecione os grupos permitidos."
                  disabled={selectedUnions.length === 0}
                />
              )}
            />

            <Controller
              name="units"
              control={control}
              render={({ field }) => (
                <SearchableMultiSelect
                  label="Unidades"
                  value={field.value ?? []}
                  onChange={field.onChange}
                  errorMessage={errors.units?.message}
                  options={unitOptions}
                  placeholder="Selecione as unidades permitidas."
                  disabled={selectedGroups.length === 0}
                />
              )}
            />

            {isEditMode && (
              <RadioGroup
                value={statusValue}
                onValueChange={(v) =>
                  setStatusValue(v as "active" | "inactive")
                }
              >
                <Label>Status</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="active" id="active" />
                    <Label htmlFor="active" className="cursor-pointer">
                      Ativo
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="inactive" id="inactive" />
                    <Label htmlFor="inactive" className="cursor-pointer">
                      Inativo
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            )}
          </div>
          <Button
            type="submit"
            variant="secondary"
            className="w-full"
            isLoading={isHydrating || isSubmitting}
            disabled={isHydrating || isSubmitting}
          >
            {isEditMode ? "Salvar" : "Cadastrar"}
          </Button>
        </form>
      </Modal>
      <SuccessModal
        isOpen={showSuccess}
        onClose={() => setShowSuccess(false)}
        title={
          successTitle ||
          (isEditMode
            ? "Usuário atualizado com sucesso!"
            : "Usuário cadastrado com sucesso!")
        }
      />
    </>
  );
}
