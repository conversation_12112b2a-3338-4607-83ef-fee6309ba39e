import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useFormContext } from "react-hook-form";
import { useState, useEffect } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCNPJ, unformatCNPJ, maskCNPJ, maskCEP } from "@/lib/format";
import { useDebounce } from "@/hooks/use-debounce";
import { getUnions } from "@/http/unions";
import { getCompanyByCNPJ } from "@/http/external";
import { useToast } from "@/hooks/use-toast";
import { InlineSpinner } from "@/components/ui/inline-spinner";

type FirstStepProps = {
  isLoading?: boolean;
  isEditMode?: boolean;
};

export function FirstStep({ isLoading, isEditMode }: FirstStepProps) {
  const {
    formState: { errors },
    register,
    setValue,
    setError,
    clearErrors,
    watch,
  } = useFormContext();

  const { toast } = useToast();

  const [cnpjDisplay, setCnpjDisplay] = useState("");
  const debouncedCnpjDigits = useDebounce(unformatCNPJ(cnpjDisplay), 400);

  // Carrega CNPJ formatado se estiver em modo de edição
  useEffect(() => {
    const currentCnpj = watch("cnpj");
    if (currentCnpj && !cnpjDisplay) {
      setCnpjDisplay(formatCNPJ(currentCnpj));
    }
  }, [watch, cnpjDisplay]);

  const handleCnpjChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const masked = maskCNPJ(e.target.value);
    setCnpjDisplay(masked);
    const clean = unformatCNPJ(masked);
    setValue("cnpj", clean);
  };

  // Verifica existência e, se não existir, busca dados externos (quando não está em edição)
  useEffect(() => {
    if (isEditMode) return;
    const digits = debouncedCnpjDigits;
    if (!digits || digits.length !== 14) return;
    let isCancelled = false;
    (async () => {
      try {
        // 1) Verifica existência por CNPJ
        const res = await getUnions({ search: digits });
        const exists = Array.isArray(res?.unions)
          ? res.unions.some(
              (u: { cnpj?: string }) =>
                (u.cnpj || "").replace(/\D/g, "") === digits
            )
          : false;
        if (isCancelled) return;
        if (exists) {
          setError("cnpj", {
            type: "manual",
            message: "Já existe um sindicato cadastrado com este CNPJ",
          });
          return; // não continua para busca externa
        }
        clearErrors("cnpj");

        // 2) Busca dados externos
        const company = await getCompanyByCNPJ(digits);
        if (isCancelled) return;
        setValue("name", company.name);
        setValue("alias", company.alias);
        setValue("registrationStatus", company.status);
        setValue("cnae", company.cnae);
        setValue("address", company.address);
      } catch (error: unknown) {
        const err = error as { response?: { data?: { message?: string } } };
        toast({
          title: "Erro",
          description:
            err?.response?.data?.message || "Erro ao buscar dados da empresa",
          variant: "destructive",
        });
      }
    })();
    return () => {
      isCancelled = true;
    };
  }, [debouncedCnpjDigits, isEditMode, setValue, clearErrors, setError, toast]);

  const street = watch("address.street") as string | undefined;
  const number = watch("address.number") as string | undefined;
  const complement = (watch("address.complement") as string | undefined) || "";
  const neighborhood =
    (watch("address.neighborhood") as string | undefined) || "";
  const city = watch("address.city") as string | undefined;
  const state = watch("address.state") as string | undefined;
  const zip = watch("address.zip") as string | undefined;

  const addressPieces: string[] = [];
  if (street && number) addressPieces.push(`${street}, ${number}`);
  if (complement) addressPieces.push(complement);
  if (neighborhood) addressPieces.push(neighborhood);
  if (city || state)
    addressPieces.push([city, state].filter(Boolean).join(", "));
  if (zip) addressPieces.push(maskCEP(zip));

  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-2 items-end">
        <Input
          label="CNPJ"
          placeholder={isEditMode ? undefined : "Digite o CNPJ"}
          trailingIcon={!isEditMode && isLoading ? <InlineSpinner /> : null}
          value={isEditMode ? formatCNPJ(watch("cnpj") || "") : cnpjDisplay}
          onChange={isEditMode ? undefined : handleCnpjChange}
          errorMessage={errors.cnpj?.message as string}
          disabled={isEditMode || (!!isLoading && !isEditMode)}
          readOnly={isEditMode}
        />
      </div>

      {isLoading ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="Situação cadastral"
          value={watch("registrationStatus") || "Preenchido automaticamente"}
          disabled
          readOnly
        />
      )}

      {isLoading ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <>
          {isEditMode ? (
            <Input
              label="Nome fantasia"
              placeholder="Digite o nome fantasia"
              {...register("alias")}
              errorMessage={errors.alias?.message as string}
            />
          ) : (
            <Input
              label="Nome fantasia"
              value={watch("alias") || "Preenchido automaticamente"}
              disabled
              readOnly
            />
          )}
        </>
      )}

      {isLoading ? (
        <Skeleton className="h-10 w-full" />
      ) : (
        <Input
          label="Razão social"
          value={watch("name") || "Preenchido automaticamente"}
          disabled
          readOnly
        />
      )}

      {isLoading ? (
        <Skeleton className="h-24 w-full" />
      ) : (
        <Textarea
          label="Endereço"
          value={
            addressPieces.length
              ? addressPieces.join("\n")
              : "Preenchido automaticamente"
          }
          disabled
          readOnly
        />
      )}
    </div>
  );
}
