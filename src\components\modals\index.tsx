"use client";

import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";

type ModalProps = {
  title: string;
  children: React.ReactNode;
  triggerButton: React.ReactNode;
  description?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  contentClassName?: string;
  headerAlign?: "left" | "center";
};

export function Modal({
  title,
  children,
  triggerButton,
  description,
  open,
  onOpenChange,
  contentClassName,
  headerAlign = "center",
}: ModalProps) {
  const [internalOpen, setInternalOpen] = useState(false);

  const isControlled = open !== undefined;
  const isOpen = isControlled ? open : internalOpen;

  const handleOpenChange = (newOpen: boolean) => {
    if (isControlled) {
      onOpenChange?.(newOpen);
    } else {
      setInternalOpen(newOpen);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{triggerButton}</DialogTrigger>
      <DialogContent
        className={`flex flex-col gap-16 p-12 overflow-y-auto max-h-[90%] ${
          contentClassName ?? ""
        }`}
      >
        <DialogHeader>
          <DialogTitle
            className={`${
              headerAlign === "left" ? "text-left sm:text-left" : "text-center"
            } text-2xl text-[#333333]`}
          >
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription
              className={`${
                headerAlign === "left" ? "text-left" : "text-center"
              } text-sm`}
            >
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        <div>{children}</div>
      </DialogContent>
    </Dialog>
  );
}
