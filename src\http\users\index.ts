import { api } from "@/services/api";
import {
  GetUserByIdResponse,
  GetUsersResponse,
  IGetUserById,
  IGetUsers,
  IUpdateUser,
  ResetUserPasswordParams,
  UpdateUserResponse,
} from "./types";

export async function getUsers({
  page,
  limit,
  search,
  roles,
  unionIds,
  businessGroupIds,
  businessUnitIds,
  consultancyIds,
  isActive,
}: IGetUsers): Promise<GetUsersResponse> {
  const params = new URLSearchParams();

  if (page !== undefined) params.append("page", page.toString());
  if (limit !== undefined) params.append("limit", limit.toString());
  if (search) params.append("search", search);
  // API espera valores separados por vírgula
  if (roles?.length) params.append("roles", roles.join(","));
  if (unionIds?.length) params.append("unionIds", unionIds.join(","));
  if (businessGroupIds?.length)
    params.append("businessGroupIds", businessGroupIds.join(","));
  if (businessUnitIds?.length)
    params.append("businessUnitIds", businessUnitIds.join(","));
  if (consultancyIds?.length)
    params.append("consultancyIds", consultancyIds.join(","));
  if (isActive !== undefined) params.append("isActive", String(isActive));

  const response = await api.get(`/api/users?${params.toString()}`);
  return response.data as GetUsersResponse;
}

export async function getUserById({
  id,
}: IGetUserById): Promise<GetUserByIdResponse> {
  const response = await api.get(`/api/users/${id}`);
  return response.data as GetUserByIdResponse;
}

export async function updateUser({
  id,
  name,
  email,
  phone,
  role,
  isActive,
  unionIds,
  businessGroupIds,
  businessUnitIds,
  consultancyIds,
}: IUpdateUser): Promise<UpdateUserResponse> {
  const response = await api.put(`/api/users/${id}`, {
    name,
    email,
    phone, // API usa "phone" no contrato
    role,
    isActive,
    unionIds,
    businessGroupIds,
    businessUnitIds,
    consultancyIds,
  });

  return response.data as UpdateUserResponse;
}

export async function resetUserPassword({ id }: ResetUserPasswordParams) {
  const response = await api.post(`/api/users/${id}/reset-password`);
  return response.data as { success: boolean; message: string };
}
