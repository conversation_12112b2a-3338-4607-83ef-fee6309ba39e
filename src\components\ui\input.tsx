/* eslint-disable react/display-name */
import * as React from "react";

import { cn } from "@/lib/utils";
import { Label } from "./label";

interface InputProps extends React.ComponentProps<"input"> {
  label?: string;
  errorMessage?: string;
  trailingIcon?: React.ReactNode;
}

const InputComponent = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<"input"> & { trailingIcon: React.ReactNode }
>(({ className, type, trailingIcon, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        `flex h-11 w-full rounded-sm border border-input bg-white px-3 py-1 ${
          trailingIcon ? "pl-7" : "pl-3"
        } text-base shadow-sm transition-colors read-only:cursor-default file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/50 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-800/40 disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-900 md:text-sm`,
        className
      )}
      ref={ref}
      {...props}
    />
  );
});

function Input({ label, errorMessage, trailingIcon, ...props }: InputProps) {
  return (
    <div className="flex flex-col gap-1 w-full">
      {label && <Label className="text-sm">{label}</Label>}
      <div className="relative">
        <div className="absolute top-1/2 left-2 transform -translate-y-1/2 opacity-30 text-black">
          {trailingIcon}
        </div>
        <InputComponent {...props} trailingIcon={trailingIcon} />
      </div>
      {errorMessage && (
        <span className="text-destructive text-xs">{errorMessage}</span>
      )}
    </div>
  );
}

export { Input };
