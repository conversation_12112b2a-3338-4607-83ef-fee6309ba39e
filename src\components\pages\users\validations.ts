import {
  optionsSchemaMandatory,
  REQUIRED_MESSAGE,
  arrayOptionsSchema,
  REQUIRED_ARRAY_MESSAGE,
} from "@/validations";
import { z } from "zod";

const SDG_ROLES = ["ADMIN_SDG", "CONTROLLER_SDG", "FINANCIAL_SDG"];

export const UserValidation = z
  .object({
    name: z.string().min(3, { message: REQUIRED_MESSAGE }),
    email: z.string().email({ message: REQUIRED_MESSAGE }),
    phoneNumber: z.string().optional(),
    profile: optionsSchemaMandatory,
    accountants: arrayOptionsSchema.optional(),
    unions: arrayOptionsSchema.optional(),
    groups: arrayOptionsSchema.optional(),
    units: arrayOptionsSchema.optional(),
  })
  .superRefine((data, ctx) => {
    if (!SDG_ROLES.includes(data.profile?.value || "")) {
      if (!data.phoneNumber || data.phoneNumber.length < 11) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_MESSAGE,
          path: ["phoneNumber"],
        });
      }

      if (!data.accountants || data.accountants.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["accountants"],
        });
      }

      if (!data.unions || data.unions.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["unions"],
        });
      }

      if (!data.groups || data.groups.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["groups"],
        });
      }

      if (!data.units || data.units.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["units"],
        });
      }
    }
  });

export type UserValidationType = z.infer<typeof UserValidation>;

// Edição de usuário (campos essenciais)
export const UserEditValidation = z.object({
  name: z.string().min(3, { message: REQUIRED_MESSAGE }),
  email: z.string().email({ message: REQUIRED_MESSAGE }),
  phone: z
    .string()
    .min(10, { message: REQUIRED_MESSAGE })
    .refine((v: string) => v.replace(/\D/g, "").length >= 10, {
      message: REQUIRED_MESSAGE,
    }),
  role: z.enum(
    [
      "ADMIN_CLIENT",
      "ADMIN_SDG",
      "ATTENDANT_SDG",
      "ACCOUNTANT",
      "CONTROLLER_SDG",
      "FINANCIAL_CLIENT",
      "FINANCIAL_SDG",
    ],
    { message: REQUIRED_MESSAGE }
  ),
  isActive: z.boolean(),
});

export type UserEditValidationType = z.infer<typeof UserEditValidation>;

// Edição (UI) – valida campos do formulário atual de edição
export const UserEditUiValidation = z
  .object({
    name: z.string().min(3, { message: REQUIRED_MESSAGE }),
    email: z.string().email({ message: REQUIRED_MESSAGE }),
    phoneNumber: z.string().optional(),
    profile: optionsSchemaMandatory,
    accountants: arrayOptionsSchema.optional(),
    unions: arrayOptionsSchema.optional(),
    groups: arrayOptionsSchema.optional(),
    units: arrayOptionsSchema.optional(),
  })
  .superRefine((data, ctx) => {
    if (!SDG_ROLES.includes(data.profile?.value || "")) {
      if (
        !data.phoneNumber ||
        data.phoneNumber.replace(/\D/g, "").length < 10
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_MESSAGE,
          path: ["phoneNumber"],
        });
      }

      if (!data.accountants || data.accountants.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["accountants"],
        });
      }

      if (!data.unions || data.unions.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["unions"],
        });
      }

      if (!data.groups || data.groups.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["groups"],
        });
      }

      if (!data.units || data.units.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: REQUIRED_ARRAY_MESSAGE,
          path: ["units"],
        });
      }
    }
  });

export type UserEditUiValidationType = z.infer<typeof UserEditUiValidation>;
