"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Label } from "./label";
import { LabelAndValue } from "@/types/labelAndValue";

interface SelectProps {
  options: LabelAndValue[];
  value?: LabelAndValue;
  onChange: (option: LabelAndValue) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  errorMessage?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  isLoading?: boolean;
  noOptionsText?: string;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoadingMore?: boolean;
}

export const Select = ({
  options,
  value,
  onChange,
  placeholder = "Selecione uma opção",
  disabled = false,
  className,
  label,
  errorMessage,
  searchable = false,
  searchPlaceholder = "Pesquisar...",
  searchValue,
  onSearchChange,
  isLoading = false,
  noOptionsText = "Nenhum resultado",
  onLoadMore,
  hasMore = false,
  isLoadingMore = false,
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localSearch, setLocalSearch] = useState("");
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (option: LabelAndValue) => {
    onChange(option);
    setIsOpen(false);
  };

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const searchQuery = searchValue ?? localSearch;
  const filteredOptions = onSearchChange
    ? options
    : options.filter((option) =>
        option.label.toLowerCase().includes(searchQuery.toLowerCase())
      );

  return (
    <div className={cn("relative w-full", className)} ref={selectRef}>
      {label && <Label className="text-sm">{label}</Label>}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={cn(
          "w-full px-4 py-2 h-11 text-left bg-white border rounded-sm shadow-sm",
          "flex items-center justify-between",
          "focus:outline-none focus:ring-1 focus:ring-gray-800/40",
          disabled &&
            "cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-900",
          "transition-colors duration-200"
        )}
        disabled={disabled}
      >
        <span
          className={
            !value
              ? "text-gray-500 text-sm text-muted-foreground/50"
              : "text-gray-900 text-sm"
          }
        >
          {value ? value.label : placeholder}
        </span>
        <ChevronDown
          className={cn(
            "w-4 h-4 transition-transform duration-200",
            isOpen ? "transform rotate-180" : ""
          )}
        />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg">
          {/** Search bar (optional) */}
          {searchable && (
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder={searchPlaceholder}
                  className="w-full pl-9 pr-4 py-2 border rounded-sm focus:outline-none bg-gray-100"
                  value={searchQuery}
                  onChange={(e) =>
                    onSearchChange
                      ? onSearchChange(e.target.value)
                      : setLocalSearch(e.target.value)
                  }
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          )}
          <ul
            className="max-h-60 overflow-auto"
            onScroll={(e) => {
              const el = e.currentTarget;
              const nearBottom =
                el.scrollTop + el.clientHeight >= el.scrollHeight - 24;
              if (nearBottom && onLoadMore && hasMore && !isLoadingMore) {
                onLoadMore();
              }
            }}
          >
            {isLoading && (
              <li className="px-4 py-2 text-gray-500">Carregando...</li>
            )}
            {filteredOptions.map((option) => (
              <li
                key={option.value}
                className={cn(
                  "px-4 py-2 cursor-pointer",
                  "hover:bg-blue-50",
                  value?.value === option.value
                    ? "bg-blue-50 "
                    : "text-gray-900"
                )}
                onClick={() => handleSelect(option)}
              >
                <Label>{option.label}</Label>
              </li>
            ))}
            {!isLoading && filteredOptions.length === 0 && (
              <li className="px-4 py-2 text-gray-500">{noOptionsText}</li>
            )}
            {isLoadingMore && (
              <li className="px-4 py-2 text-gray-500">Carregando...</li>
            )}
          </ul>
        </div>
      )}
      {errorMessage && (
        <span className="text-destructive text-xs">{errorMessage}</span>
      )}
    </div>
  );
};
