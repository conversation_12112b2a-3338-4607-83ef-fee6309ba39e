export type IAddress = {
  street: string;
  number: string;
  complement?: string;
  zip: string;
  neighborhood: string;
  city: string;
  state: string;
};

export type IContact = {
  name: string;
  phone: string;
  email: string;
};

export type ICreateBusinessGroup = {
  unionId: string;
  consultancyId?: string;
  cnpj: string;
  alias: string;
  name: string;
  address: IAddress;
  cnae: string;
  contact: IContact;
  financeContact?: Partial<IContact>;
};

export type IGetBusinessGroups = {
  page?: number;
  limit?: number;
  search?: string;
  unionIds?: string[];
  consultancyIds?: string[];
};

export type IGetBusinessGroupById = {
  id: string;
};

export type IUpdateBusinessGroup = {
  id: string;
  alias?: string;
  contact?: Partial<IContact>;
  financeContact?: Partial<IContact>;
};
