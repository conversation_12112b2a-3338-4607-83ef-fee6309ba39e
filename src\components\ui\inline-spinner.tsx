import { cn } from "@/lib/utils";

type InlineSpinnerProps = {
  size?: "xs" | "sm" | "md" | "lg";
  className?: string;
  ariaLabel?: string;
};

const sizeToClass: Record<NonNullable<InlineSpinnerProps["size"]>, string> = {
  xs: "w-3 h-3 border-2",
  sm: "w-4 h-4 border-2",
  md: "w-5 h-5 border-2",
  lg: "w-6 h-6 border-2",
};

export function InlineSpinner({
  size = "sm",
  className,
  ariaLabel,
}: InlineSpinnerProps) {
  return (
    <div
      role="status"
      aria-label={ariaLabel || "Carregando"}
      className={cn(
        "rounded-full animate-spin border-solid border-primary/80",
        sizeToClass[size],
        "border-t-transparent",
        className
      )}
    />
  );
}
