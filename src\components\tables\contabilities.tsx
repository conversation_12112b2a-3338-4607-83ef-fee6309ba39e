"use client";

import { useState } from "react";
import { DataTable } from "../ui/datatable";
import { Button } from "../ui/button";
import { Edit2, ListFilter } from "lucide-react";
import { Column } from "@/types/table";
import { Modal } from "@/components/modals";
import { CheckboxList } from "@/components/ui/filterCheckbox";
import { useQuery } from "@tanstack/react-query";
import { getConsultancies } from "@/http/consultancies";
import {
  GetConsultanciesResponse,
  Consultancy,
} from "@/http/consultancies/types";
import { formatCNPJ, maskPhoneBR } from "@/lib/format";
import { ContabilityModalForm } from "../pages/contabilities/FormModal";
import { StatusBadge } from "../ui/status-badge";

type ConsultancyRow = {
  id: string;
  cnpj: string;
  alias: string;
  name: string;
  contact: { name: string; email: string; phone: string };
  isActive: boolean;
};

type ConsultancyApi = Consultancy;
type GetConsultanciesPayload = GetConsultanciesResponse;

export function ContabilitiesTable({ search = "" }: { search?: string }) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filterIsActive, setFilterIsActive] = useState<boolean | undefined>(
    undefined
  );
  const [uiSelectedStatus, setUiSelectedStatus] = useState<string[]>([]);

  const { data, isLoading } = useQuery({
    queryKey: ["consultancies", currentPage, pageSize, search, filterIsActive],
    queryFn: async () => {
      const res = await getConsultancies({
        page: currentPage,
        limit: pageSize,
        search: search || undefined,
        isActive: filterIsActive,
      });
      const payload =
        (res as unknown as { data?: GetConsultanciesPayload }).data ??
        (res as unknown as GetConsultanciesPayload);
      return payload;
    },
  });

  const sourceList: ConsultancyApi[] = data?.consultancies ?? [];

  const rows: ConsultancyRow[] = sourceList.map((c) => ({
    id: String(c.id),
    cnpj: String(c.cnpj ?? ""),
    alias: String(c.alias ?? ""),
    name: String(c.name ?? ""),
    contact: {
      name: String(c.contact?.name ?? ""),
      email: String(c.contact?.email ?? ""),
      phone: String(c.contact?.phone ?? ""),
    },
    isActive: Boolean(c.isActive),
  }));

  const totalPages = data?.totalPages || 0;

  const columns: Column<ConsultancyRow>[] = [
    { header: "CNPJ", render: (r) => formatCNPJ(r.cnpj) },
    { header: "Nome fantasia", render: (r) => r.alias },
    { header: "Nome do contato", render: (r) => r.contact.name },
    { header: "Email do contato", render: (r) => r.contact.email },
    {
      header: "Telefone do contato",
      render: (r) => maskPhoneBR(r.contact.phone),
    },
    { header: "Status", render: (r) => <StatusBadge isActive={r.isActive} /> },
    {
      isActionColumn: true,
      header: "",
      render: (row) => (
        <ContabilityModalForm
          isEditMode
          consultancy={row}
          triggerButton={
            <Button className="w-7 h-7">
              <Edit2 size={16} className="w-4 h-4" />
            </Button>
          }
        />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={rows}
      keyExtractor={(item) => item.id}
      className="w-full p-8"
      isLoading={isLoading}
      openFilterModal={
        <Modal
          title="Filtrar por"
          headerAlign="left"
          triggerButton={
            <Button variant="ghost" className="px-2 h-8 border border-gray-200">
              <ListFilter />
            </Button>
          }
        >
          <div className="flex flex-col gap-6">
            <CheckboxList
              hideSearchBar
              title="Status"
              options={[
                { label: "Ativo", value: "active" },
                { label: "Inativo", value: "inactive" },
              ]}
              selectedValues={uiSelectedStatus}
              onSelectionChange={setUiSelectedStatus}
            />
            <div className="flex w-full gap-3">
              <Button
                className="flex-1"
                variant="secondary"
                onClick={() => {
                  if (
                    uiSelectedStatus.length === 0 ||
                    uiSelectedStatus.length === 2
                  ) {
                    setFilterIsActive(undefined);
                  } else {
                    setFilterIsActive(uiSelectedStatus[0] === "active");
                  }
                }}
              >
                Aplicar
              </Button>
              <Button
                className="flex-1"
                variant="outline"
                onClick={() => {
                  setUiSelectedStatus([]);
                  setFilterIsActive(undefined);
                }}
              >
                Limpar filtros
              </Button>
            </div>
          </div>
        </Modal>
      }
      pagination={{
        currentPage,
        totalPages,
        onPageChange: setCurrentPage,
        pageSize,
        onPageSizeChange: (newSize) => {
          setPageSize(newSize);
          setCurrentPage(1);
        },
      }}
    />
  );
}
